import { StatusCodes } from "http-status-codes";
import {
  UserRequest,
  request_status,
  leave_calculation_type as request_calculation_type
} from "../models/UserRequest";
import moment from "moment";
import { sequelize } from "../models";
import { Op, QueryTypes, Sequelize } from "sequelize";
import { formatNumber, getPaginatedItems, getPagination } from "../helper/utils";
import { createNotification, getLeaveRequestsByDateRanges, readHTMLFile, leavePolicyRules, getUserLeaveBalanceFunc, getSuperAdminUserIdEnhanced, getGeneralSettingObj, calculateLeaveTotal, sendEmailNotification, getOrganizationLogo, validateModulePermission, generateRoleBasedUserFilter } from "../helper/common";
import { User, user_status, marital_status } from "../models/User";
import { DSRCONSTANT, EMAIL_ADDRESS, NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, REDIRECTION_TYPE, ROLE_CONSTANT, ROLE_PERMISSIONS } from "../helper/constant";
import { Branch, branch_status } from "../models/Branch";
import { Department, department_status } from "../models/Department";
import { Role, role_status } from "../models/Role";
import { Role as MORole } from "../models/MORole";
import { Request, Response } from "express";
import { LeaveTypeModel, status as leaveTypeStatus } from "../models/LeaveType";
import { LeavePolicyRelationModel, status as leaveRelationStatus } from "../models/LeavePolicyRelation";
import { LeavePolicyModel, status as leavePolicyStatus } from "../models/LeavePolicy";
import { UserMeta } from "../models/UserMeta";
import { fetchUserLeavePolicies } from "../controller/contract.controller";
import { UserBranch } from "../models/UserBranch";
import { LeaveRule, leave_rule_status } from "../models/LeaveRule";
import { generateFile } from "../helper/fileGeneration.service";
import path from "path";
import handlebars from "handlebars";
import { LeaveAccuralPolicy, status as leave_accural_status, status } from "../models/LeaveAccuralPolicy";
import { user_leave_policy_status, UserLeavePolicy } from "../models/UserLeavePolicy";
import { Resignation, resignation_status } from "../models/Resignation";
import { LeaveRestrictionPolicy } from "../models/LeaveRestrictionPolicy";
import { approval_type, LeaveApprovalMetaPolicy } from "../models/LeaveApprovalMetaPolicy";
import { LeaveApprovalPolicy } from "../models/LeaveApprovalPolicy";
import { user_weekday_status, UserWeekDay } from "../models/UserWeekDay";
import { UserRole } from "../models/UserRole";

/**
 *   Apply for leave
 * @param req
 * @param res
 * @returns
 */

const leaveApply = async (req: any, res: any) => {
  try {
    let leave_request_approval_userId
    let { leave_days } = req.body
    const {
      subject = "",
      request_reason,
      leave_type,
      start_date,
      end_date,
      role_id,
      leave_request_type,
      // has_unlimited,
      duration_type,
      // document_file,
      leave_days_obj
    } = req.body;
    const leaveYear = moment(start_date).year()
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });

    const filterStartDate: any = moment(start_date, 'YYYY-MM-DD').startOf('day').format('YYYY-MM-DD')
    const filterEndDate: any = moment(end_date, 'YYYY-MM-DD').startOf('day').format('YYYY-MM-DD')
    // Check if there is already a pending leave request for the same days
    const existingPendingLeave: any = await UserRequest.findOne({
      attributes: ['id', 'request_status'],
      where: {
        from_user_id: req.user.id,
        [Op.or]: [
          {
            [Op.and]: [Sequelize.literal(`DATE(start_date) = '${filterStartDate}'`)], // Exact start date match
          },
          {
            [Op.and]: [
              Sequelize.literal(`DATE(start_date) <= '${filterEndDate}'`), // Leave starts on or before the requested end date
              Sequelize.literal(`DATE(end_date) >= '${filterStartDate}'`), // Leave ends on or after the requested start date
            ],
          },
        ]
      }, raw: true
    });
    if (existingPendingLeave) {
      /** check if user status is pending then throw error */
      if (existingPendingLeave.request_status == request_status.PENDING) {
        return res
          .status(400)
          .json({ status: false, message: res.__("FAIL_LEAVE_ALREADY_PENDING") });
      }
      /* check if user status is approved and tried to apply same day leave then throw error */
      if (existingPendingLeave.request_status == request_status.APPROVED) {
        return res
          .status(400)
          .json({ status: false, message: res.__("ERROR_FAIL_LEAVE_ALREADY_APPLIED") });
      }
    }

    const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
    /** check all Leave policy Rules */
    req.body.user_gender = getUserDetail.user_gender
    req.body.marital_status = getUserDetail.marital_status == marital_status.SINGLE ? 'unmarried' : getUserDetail.marital_status
    req.body.user_joining_date = getUserDetail.user_joining_date
    req.body.user_id = req.user.id
    const getLeaveRules: any = await leavePolicyRules(leave_request_type, req.body, req.user.organization_id)
    if (getLeaveRules.status == false) {
      const message: any = res.__(getLeaveRules.message);
      if (getLeaveRules.hours) {
        const message = res.__(getLeaveRules.message, { hours: getLeaveRules.hours });
        return res
          .status(400)
          .json({ status: false, message: message });
      }
      if (getLeaveRules.joining_date) {
        const message = res.__(getLeaveRules.message, { joining_date: getLeaveRules.joining_date });
        return res
          .status(400)
          .json({ status: false, message: message });
      }
      if (getLeaveRules.contract_date) {
        const message = res.__(getLeaveRules.message, { contract_date: getLeaveRules.contract_date });
        return res
          .status(400)
          .json({ status: false, message: message });
      }
      if (getLeaveRules.advance_hours) {
        const message = res.__(getLeaveRules.message, { advance_hours: getLeaveRules.advance_hours });
        return res
          .status(400)
          .json({ status: false, message: message });
      }

      if (getLeaveRules.max_consecutive_days) {
        const message = res.__(getLeaveRules.message, { max_consecutive_days: getLeaveRules.max_consecutive_days });
        return res
          .status(400)
          .json({ status: false, message: message });
      }

      if (getLeaveRules.leave_gap) {
        const message = res.__(getLeaveRules.message, { leave_gap: getLeaveRules.leave_gap });
        return res
          .status(400)
          .json({ status: false, message: message });
      }

      if (getLeaveRules.max_leaves_allowed) {
        const message = res.__(getLeaveRules.message, { max_leaves_allowed: getLeaveRules.max_leaves_allowed, policy_type: getLeaveRules.policy_type });
        return res
          .status(400)
          .json({ status: false, message: message });
      }

      if (getLeaveRules.isEligable) {
        const message = res.__(getLeaveRules.message, { pendingDays: getLeaveRules.pendingDays });
        return res
          .status(400)
          .json({ status: false, message: message });
      }

      if (getLeaveRules.leave_limit) {
        const message = res.__(getLeaveRules.message, { leave_limit: getLeaveRules.leave_limit });
        return res
          .status(400)
          .json({ status: false, message: message });
      }
      return res
        .status(400)
        .json({ status: false, message: message });
    }

    /** check if status is true */
    if (getLeaveRules.status == true) {
      /** then assign new leave days for holiday also */
      leave_days = getLeaveRules.message > 0 ? getLeaveRules.message : leave_days
      leave_request_approval_userId = getLeaveRules.leave_request_approval_userId
    }

    const applyLeave = await UserRequest.setHeaders(req).create({
      request_reason: request_reason,
      request_subject: subject,
      request_type: leave_type,
      role_id,
      leave_days,
      start_date,
      end_date,
      from_user_id: req.user.id,
      created_by: req.user.id,
      updated_by: req.user.id,
      request_status: request_status.PENDING,
      leave_request_type,
      leave_days_obj,
      leave_old_calculation: leave_days,
      leave_period_type: findGeneralSetting?.leave_period_type,
      duration_type: duration_type
    } as any);

    if (applyLeave) {
      /** update user leave balance */
      /** get user assigned policy for this request */

      const leavePolicyData = (await getUserLeaveBalanceFunc(req.user.id, leaveYear, null, leave_request_type, null, null, false, findGeneralSetting?.leave_period_type, req.user.organization_id)).data

      /** check if leave request approval is not then set it's status approved directly  */
      if (getLeaveRules.status == true && getLeaveRules.auto_approval == true) {

        /** If an employee takes leave during their resignation notice period, extend the notice period by the number of leave days taken. */
        /** check user is on notice period */
        const checkUserStatus = await Resignation.findOne({ where: { user_id: req.user.id, resignation_status: resignation_status.ACCEPTED }, attributes: ['id', 'last_serving_date'], raw: true })
        /** If user is on notice period then get user assigned policy for this request */


        if (checkUserStatus) {
          if (leavePolicyData.length > 0) {
            /** get Tab-3 policy for check extend notice period scenario */
            const getLeavePolicy = await LeaveRestrictionPolicy.findOne({ where: { leave_accural_policy_id: leavePolicyData[0].leave_accural_policy?.id }, raw: true })
            if (getLeavePolicy) {
              if (getLeavePolicy.extend_notice_period == true) {
                const updateObj: any = {
                  last_serving_date: moment(checkUserStatus.last_serving_date).add(leave_days, 'days')
                }
                await Resignation.update(updateObj, { where: { id: checkUserStatus.id } })
              }
            }
          }
        }

        /** Update leave request from pending to approved */
        await UserRequest.update(
          { request_status: request_status.APPROVED },
          { where: { id: applyLeave.id } }
        );
      }
      /** Commented below code due to leave policy user, now from policy we will select user and send notification only that user 
       *  const getAdmins = await getAdminStaffs(getUserDetail.branch_id, req.user.id, role_id); */

      /** get users data */
      if (leave_request_approval_userId) {
        const getAdmins: any[] = [];

        for (const userId of leave_request_approval_userId) {
          // Get user details
          const user: any = await User.findOne({
            where: { id: userId, organization_id: req.user.organization_id } as any,
            raw: true
          });
          const findUserRoles = await UserRole.findAll({
            where: { user_id: userId },
            include: [
              {
                model: Role,
                as: "role",
                attributes: ["id", "role_name", "parent_role_id"],
              },
            ],
            nest: true,
            raw: true,
          });


          const userRoleIds = findUserRoles.length > 0 ? findUserRoles.map((ur: any) => ur.role?.id).filter(Boolean) : []
          // Check if user has a matching role (from the allowed roles)
          let hasMatchingParent = false;
          if (userRoleIds.some((roleId: any) => roleId < 6)) {
            hasMatchingParent = true;
          }
          // If has a matching parent role OR branch matches, include user
          if (hasMatchingParent) {
            getAdmins.push(userId);
          } else if (user.branch_id === getUserDetail.branch_id) {
            getAdmins.push(userId);
          }
        }

        if (getAdmins.length > 0) {
          const getLeaveTypeName = await LeaveTypeModel.findOne({ where: { id: leave_request_type, organization_id: req.user.organization_id }, raw: true })
          const templateData: any = {
            name: `${getUserDetail.user_first_name} ${getUserDetail.user_last_name}`,
            date: `${moment(start_date).format("DD/MM/YYYY")} to ${moment(end_date).format("DD/MM/YYYY")}`,
            type: getLeaveTypeName?.name,
            reason: request_reason,
            ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
            LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
            ADDRESS: EMAIL_ADDRESS.ADDRESS,
            PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
            EMAIL: EMAIL_ADDRESS.EMAIL,
            ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
            smtpConfig: 'INFO',
            mail_type: 'leave_request'
          };
          /** check auto approval status is true then send push notification to selected admin's  */
          if (getLeaveRules.status == true && getLeaveRules.auto_approval == false) {
            const findUser = await User.findAll({ attributes: ['id', 'appToken', 'webAppToken'], where: { id: { [Op.in]: getAdmins }, organization_id: req.user.organization_id }, raw: true })
            await createNotification(findUser, req, NOTIFICATION_TYPE.ADMIN, NOTIFICATIONCONSTANT.LEAVE_APPLY.content(templateData.name), NOTIFICATIONCONSTANT.LEAVE_APPLY.heading, REDIRECTION_TYPE.LEAVE, applyLeave.id, { leave_id: applyLeave.id, type: "staff" })
          }
          /** check email status is true then and then send email to that users  */
          if (getLeaveRules.status == true && getLeaveRules.email_status == true) {
            templateData.email = getAdmins.map((admins: any) => admins.user_email).join(",")
            await sendEmailNotification(templateData)
          }
        }
      }
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_LEAVE_APPLIED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("FAIL_LEAVE_APPLY_PROCESS") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Get own leave
 * @param req
 * @param res
 * @returns
 */

const getOwnLeaveList = async (req: any, res: any) => {
  try {
    const { tab, page, size, year, leave_type_id, search, request_status } = req.query;
    const { limit, offset } = getPagination(page, size);
    const currentDate = moment().startOf("day").toDate();

    const whereObj: any = {
      from_user_id: req.user.id
    };
    if (request_status) {
      whereObj.request_status = request_status;
    }
    if (leave_type_id) {
      whereObj.leave_request_type = leave_type_id;
    }
    const andConditions: any[] = []; // Ensure both filters are applied together

    if (search) {
      andConditions.push({
        [Op.or]: [
          { request_reason: { [Op.like]: `%${search}%` } },
          { request_subject: { [Op.like]: `%${search}%` } }
        ]
      });
    }

    if (year) {
      const startOfYear = year ? moment({ year, month: 0, day: 1 }).startOf('year').toDate() : null;
      const endOfYear = year ? moment({ year, month: 11, day: 31 }).endOf('year').toDate() : null;
      andConditions.push({
        [Op.or]: [
          { start_date: { [Op.between]: [startOfYear, endOfYear] } },
          { end_date: { [Op.between]: [startOfYear, endOfYear] } }
        ]
      });
    }

    // Apply filters
    if (andConditions.length > 0) {
      whereObj[Op.and] = andConditions;
    }

    // Apply tab conditions **without overwriting** previous conditions
    if (tab === "ongoing") {
      andConditions.push({
        start_date: { [Op.gte]: currentDate },
        end_date: { [Op.gte]: currentDate },
      });
    } else if (tab === "past") {
      andConditions.push({
        start_date: { [Op.lt]: currentDate },
        end_date: { [Op.lt]: currentDate },
      });
    }

    // Only apply Op.or if there are conditions
    if (andConditions.length > 0) {
      whereObj[Op.and] = andConditions;
    }

    const leaveListObj: any = {
      include: [
        {
          model: User,
          as: "request_approved_users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_approved_users.user_first_name"),
                " ",
                sequelize.col("request_approved_users.user_last_name")
              ),
              "user_full_name",
            ],
            "employment_number",
          ],
        },
        {
          model: User,
          as: "request_from_users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_from_users.user_first_name"),
                " ",
                sequelize.col("request_from_users.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            [
              sequelize.literal(
                `CASE 
            WHEN request_from_users.user_avatar IS NULL OR request_from_users.user_avatar = '' THEN ''
            WHEN NOT request_from_users.user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = request_from_users.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', request_from_users.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = request_from_users.user_avatar))
          END`
              ),
              "user_avatar_link",
            ],
            "employment_number"
          ],
          where: { organization_id: req.user.organization_id },
          include: [
            {
              model: UserWeekDay,
              as: "user_week_day",
              attributes: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
              where: { user_weekday_status: user_weekday_status.ACTIVE, user_id: sequelize.col("request_from_users.id") },
              required: false,
            }
          ],
          required: true,
        },
        {
          model: LeaveTypeModel,
          as: "leave_request_type_list",
          attributes: ["id", "name", "leave_type_color", "leave_deduction_type"],
          where: { organization_id: req.user.organization_id },
        },
      ],
      attributes: [
        "id",
        "request_reason",
        "start_date",
        "end_date",
        "leave_days",
        "request_type",
        "request_status",
        "approved_by_reason",
        "request_subject",
        "createdAt",
        "role_id",
        "duration_type",
        "leave_deduction_type",
        "updatedAt",
        "leave_days_obj",
        "leave_calculation_type",
        "leave_period_type",
      ],
      where: whereObj,
      order: [["createdAt", "DESC"]],
      nest: true,
      raw: true,
    };

    if (page && size) {
      leaveListObj.limit = Number(limit);
      leaveListObj.offset = Number(offset);
    }
    const { rows: getLeaveList, count } = await UserRequest.findAndCountAll(leaveListObj);
    const data = getLeaveList.map((leave: any) => {
      return {
        ...leave,
        user_request_week_day: leave?.request_from_users?.user_week_day,
      };
    });
    const { total_pages } = getPaginatedItems(
      size,
      page,
      count || 0,
    );
    if (getLeaveList.length > 0) {
      res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_LEAVE_DATA_FETCHED"),
        data: data,
        count: count,
        page: parseInt(page),
        size: parseInt(size),
        total_pages,
      });
    } else {
      res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("FAIL_LEAVE_DATA_NOT_FOUND"),
        data: [],
        count: 0,
        page: 0,
        size: 0,
        total_pages: 0,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Get staff leave
 * @param req
 * @param res
 * @returns
 */

const getStaffLeaveList = async (req: any, res: any) => {
  try {
    const {
      size,
      page,
      search,
      branch_id,
      department_id,
      status,
      role_id,
      start_date,
      end_date,
    } = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'organization_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id }, raw: true
    });

    // Try new MORole-based permission system first, then fallback to old system
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      'change_request', // Change Request module slug
      ROLE_PERMISSIONS.VIEW,
      req?.headers?.["platform-type"]
    );

    // Enhanced admin permission check (combines both old and new systems)
    // const checkAdminPermission = await permittedForAdminEnhanced(
    //   req.user?.id,
    //   req.user.organization_id,
    //   [
    //     ROLE_CONSTANT.SUPER_ADMIN,
    //     ROLE_CONSTANT.ADMIN,
    //     ROLE_CONSTANT.DIRECTOR,
    //     ROLE_CONSTANT.ACCOUNTANT,
    //     ROLE_CONSTANT.AREA_MANAGER,
    //     ROLE_CONSTANT.HR,
    //     ROLE_CONSTANT.BRANCH_MANAGER,
    //     ROLE_CONSTANT.HOTEL_MANAGER,
    //     ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
    //     ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER
    //   ]
    // );

    // Also check old permission system for backward compatibility
    const oldPermissionCheck = findUserRole && [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.ACCOUNTANT,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
      ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
      ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER
    ].includes(findUserRole?.role_name);

    // User has permission if any check passes
    const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

    if (!hasPermission) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const whereObj: any = {
      from_user_id: { [Op.not]: req.user.id },
    };
    if (status) {
      whereObj.request_status = status;
    }
    if (start_date && end_date) {
      whereObj.start_date = { [Op.gte]: start_date };
      whereObj.end_date = { [Op.lte]: end_date };
    } else if (start_date && !end_date) {
      whereObj[Op.and] = [
        sequelize.where(sequelize.fn("DATE", sequelize.col("start_date")), {
          [Op.lte]: start_date,
        }),
        sequelize.where(sequelize.fn("DATE", sequelize.col("end_date")), {
          [Op.gte]: start_date,
        }),
      ];
    } else if (end_date && !start_date) {
      whereObj[Op.and] = [
        sequelize.where(sequelize.fn("DATE", sequelize.col("start_date")), {
          [Op.lte]: end_date,
        }),
        sequelize.where(sequelize.fn("DATE", sequelize.col("end_date")), {
          [Op.gte]: end_date,
        }),
      ];
    }
    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("request_from_users.user_first_name"),
            " ",
            Sequelize.col("request_from_users.user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        { request_reason: { [Op.like]: `%${search}%` } },
      ];
    }
    const getLeaveListQuery: any = {
      include: [
        {
          model: User,
          as: "request_from_users",
          where: { organization_id: req.user.organization_id },
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_from_users.user_first_name"),
                " ",
                sequelize.col("request_from_users.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            [
              sequelize.literal(
                `CASE 
            WHEN request_from_users.user_avatar IS NULL OR request_from_users.user_avatar = '' THEN ''
            WHEN NOT request_from_users.user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = request_from_users.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', request_from_users.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = request_from_users.user_avatar))
          END`
              ),
              "user_avatar_link",
            ],
            "employment_number"
          ],
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: branch_id
                ? { id: branch_id, branch_status: branch_status.ACTIVE }
                : { branch_status: branch_status.ACTIVE },
              required: branch_id ? true : false,
            },
            {
              model: Department,
              as: "department",
              attributes: ["department_name"],
              where: department_id
                ? {
                  id: department_id,
                  department_status: department_status.ACTIVE,
                }
                : { department_status: department_status.ACTIVE },
              required: department_id ? true : false,
            },
            {
              model: Role,
              as: "role",
              attributes: ["role_name"],
              where: role_id
                ? { id: role_id, role_status: role_status.ACTIVE }
                : { role_status: role_status.ACTIVE },
              required: role_id ? true : false,
            },
          ],
          required: true,
        },
        {
          model: User,
          as: "request_approved_users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_approved_users.user_first_name"),
                " ",
                sequelize.col("request_approved_users.user_last_name"),
              ),
              "user_full_name",
            ],
            "employment_number"
          ],
        },
        {
          model: MORole,
          as: "leave_role_request",
          attributes: ["id", "role_name"]
        },
        {
          model: LeaveTypeModel,
          as: "leave_request_type_list",
          attributes: ['id', 'name', "leave_type_color", "leave_deduction_type"],
          required: false,
          where: { organization_id: req.user.organization_id }
        }
      ],
      attributes: [
        "id",
        "request_reason",
        "start_date",
        "end_date",
        "leave_days",
        "request_type",
        "request_status",
        "approved_by_reason",
        "request_subject",
        "createdAt",
        "duration_type",
        "role_id",
        "updatedAt",
        "leave_deduction_type",
        "leave_days_obj",
        "leave_calculation_type",
        "leave_period_type"
      ],
      where: whereObj,
      order: [['createdAt', 'DESC']],
      nest: true,
      raw: true,
    };

    if (page && size) {
      getLeaveListQuery.limit = Number(limit);
      getLeaveListQuery.offset = Number(offset);
    }

    if (
      (findUserRole &&
        (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
          findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
          findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
          findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER || findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER
        ))) {

      const findUserBranch = await UserBranch.findAll({
        where: {
          user_id: req.user.id
        }, raw: true
      })
      if (findUserRole.role_name == ROLE_CONSTANT.AREA_MANAGER && findUserBranch.length > 0) {
        whereObj[Op.and] = [
          Sequelize.where(Sequelize.col("request_from_users.branch_id"), {
            [Op.in]: findUserBranch.map((branch: any) => branch.branch_id),
          }),
        ];
      } else {
        whereObj[Op.and] = [
          Sequelize.where(Sequelize.col("request_from_users.branch_id"), {
            [Op.eq]: getUserDetail?.branch_id,
          }),
        ];
      }
      const getChildRolesQuery = `
        WITH RECURSIVE ChildRoles AS (
          SELECT id, role_name, parent_role_id
          FROM nv_roles
          WHERE id = :activeRoleId
          UNION ALL
          SELECT r.id, r.role_name, r.parent_role_id
          FROM nv_roles r
          INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
        )
        SELECT id
        FROM ChildRoles
        WHERE id != :activeRoleId
      `;

      // Execute recursive query to find child roles
      const getChildRoles = await sequelize.query(getChildRolesQuery, {
        replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
        type: QueryTypes.SELECT,
      });

      // Build WHERE clause for user roles based on child roles
      let whereStr = '';
      getChildRoles.forEach((child_role: any, index: number) => {
        if (index > 0) {
          whereStr += ' OR ';
        }
        whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = request_from_users.id)) > 0`;
      });

      if (whereStr) {
        whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
      }
    }
    const { rows: getLeaveList, count } = await UserRequest.findAndCountAll(getLeaveListQuery);
    const { total_pages } = getPaginatedItems(
      size,
      page,
      count || 0,
    );
    res.status(StatusCodes.OK).json({
      status: true,
      userList: getLeaveList,
      message: res.__("SUCCESS_FETCHED"),
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Approve Reject Request
 * @param req
 * @param res
 * @returns
 */

const approveRejectRequest = async (req: any, res: any) => {
  try {
    const { action, remark, request_id, leave_deduction_type, leave_days, leave_days_obj } = req.body;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'user_first_name', 'user_last_name', 'user_middle_name', 'user_email', 'organization_id', 'branch_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    // const getUserCurrentRole: any = await Role.findOne({ where: { id: req.headers["platform-type"] == "web" ? getUserDetail?.web_user_active_role_id : getUserDetail?.user_active_role_id, role_status: role_status.ACTIVE } })
    // if (![ROLE_CONSTANT.SUPER_ADMIN,
    // ROLE_CONSTANT.ADMIN,
    // ROLE_CONSTANT.DIRECTOR,
    // ROLE_CONSTANT.AREA_MANAGER,
    // ROLE_CONSTANT.HR,
    // ROLE_CONSTANT.BRANCH_MANAGER,
    // ROLE_CONSTANT.HOTEL_MANAGER,
    // ].includes(getUserCurrentRole?.role_name)) {
    //   return res.status(StatusCodes.EXPECTATION_FAILED).json({
    //     status: false,
    //     message: res.__("PERMISSION_DENIED"),
    //   });
    // }
    const findLeaveRequest = await UserRequest.findOne({
      where: { id: request_id }, raw: true
    });
    const getRequestUserDetail: any = await User.findOne({
      attributes: ['id', 'user_first_name', 'user_last_name', 'user_email', 'appToken', 'webAppToken'],
      where: { id: findLeaveRequest?.from_user_id, organization_id: req.user.organization_id }, raw: true
    });
    if (!findLeaveRequest) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_REQUEST_NOT_FOUND"),
      });
    }
    /** Get user assign policy */
    const getUserPolicy: any = await UserLeavePolicy.findOne({
      where: { user_id: getRequestUserDetail.id, user_leave_policy_status: user_leave_policy_status.ACTIVE },
      include: [{
        attributes: ['id'],
        model: LeaveAccuralPolicy,
        as: 'user_leave_policy',
        include: [{
          attributes: ['id'],
          model: LeaveTypeModel,
          as: 'leave_accural_leave_type_id',
          where: { id: findLeaveRequest.leave_request_type, status: status.ACTIVE, organization_id: req.user.organization_id }
        }]
      }],
      raw: true, nest: true
    })


    const getLeaveApprovalRules: any = await LeaveApprovalPolicy.findOne({ where: { leave_accural_policy_id: getUserPolicy?.user_leave_policy?.id }, raw: true })
    const getLeaveApprovalMetaRules: any = await LeaveApprovalMetaPolicy.findAll({ where: { leave_accural_policy_id: getUserPolicy?.user_leave_policy?.id, approval_type: approval_type.LEAVE }, raw: true })
    if (getLeaveApprovalRules && getLeaveApprovalRules?.leave_request_require_approval == true) {
      const getAdmins: any[] = [];
      /** Removed if any repaated userid exist */

      const userIds = [...new Set(getLeaveApprovalMetaRules.map((item: any) => item.user_id))];
      if (userIds.length > 0) {
        for (const userId of userIds) {
          // Get user details
          const user: any = await User.findOne({
            attributes: ['branch_id'],
            where: { id: userId, organization_id: req.user.organization_id } as any,
            raw: true
          });
          // Get user role using MORole system if available, otherwise fallback to old system
          let userRoleIds: number[] = [];
          let hasMatchingParent = false;

          if (user.user_role_id) {
            // Use MORole system - check if user has admin-level role
            const moRole: any = await MORole.findOne({
              where: {
                id: user.user_role_id,
                organization_id: req.user.organization_id,
                role_status: 'active'
              },
              attributes: ["id", "role_name", "parent_role_id"],
              raw: true
            });

            if (moRole) {
              userRoleIds = [moRole.id];
              // Check if it's an admin-level role (you may need to adjust this logic based on your MORole hierarchy)
              const adminRoles = [ROLE_CONSTANT.SUPER_ADMIN, ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.DIRECTOR, ROLE_CONSTANT.HR, ROLE_CONSTANT.AREA_MANAGER];
              hasMatchingParent = adminRoles.includes(moRole.role_name);
            }
          } else {
            // Fallback to old UserRole system
            const findUserRoles = await UserRole.findAll({
              where: { user_id: userId } as any,
              include: [
                {
                  model: Role,
                  as: "role",
                  attributes: ["id", "role_name", "parent_role_id"],
                },
              ],
              nest: true,
              raw: true,
            });

            userRoleIds = findUserRoles.length > 0 ? findUserRoles.map((ur: any) => ur.role?.id).filter(Boolean) : [];
            // Check if user has a matching role (from the allowed roles)
            if (userRoleIds.some((roleId: any) => roleId < 6)) {
              hasMatchingParent = true;
            }
          }
          // If has a matching parent role OR branch matches, include user
          if (hasMatchingParent) {
            getAdmins.push(userId);
          } else if (user.branch_id === getUserDetail.branch_id) {
            getAdmins.push(userId);
          }
        }
      }
      const getUserIds = await getSuperAdminUserIdEnhanced(req.user?.organization_id)
      userIds.push(...getUserIds)
      if (!userIds.includes(req.user.id)) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("PERMISSION_DENIED"),
        });
      }

    }
    const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
    const leaveYear = moment(findLeaveRequest?.start_date).year()
    const leavePolicyData = (await getUserLeaveBalanceFunc(getRequestUserDetail.id, leaveYear, null, findLeaveRequest.leave_request_type, null, null, false, findGeneralSetting?.leave_period_type, req.user.organization_id)).data
    const userRequestObj: any = {
      request_status: action,
      approved_by_reason: remark,
      request_approved_by: req.user.id,
      leave_calculation_type: findGeneralSetting?.leave_calculation_type,
      updated_by: req.user.id,
      leave_deduction_type
    }

    let leaveHour;

    if (findGeneralSetting?.leave_period_type === 'day') {
      const totalDays = leave_days_obj
        ? await calculateLeaveTotal(leave_days_obj, 'day')
        : leave_days;
      leaveHour = totalDays;
    } else if (findGeneralSetting?.leave_period_type === 'hour') {
      const totalHours = leave_days_obj
        ? await calculateLeaveTotal(leave_days_obj, 'hour')
        : leave_days;
      leaveHour = totalHours
    } else {
      leaveHour = leave_days; // fallback if type is not day/hour
    }
    const findDiffrence = leaveHour - findLeaveRequest.leave_days || 0
    const userRemainingLeave = leavePolicyData[0]?.user_remaining_leave   // Default to 0 if null
    if (findGeneralSetting?.leave_calculation_type == request_calculation_type.MANUAL) {
      if (findDiffrence > 0) {
        userRemainingLeave - findDiffrence
      }

      if (userRemainingLeave < 0 && leavePolicyData[0]?.has_leave_unlimited != true) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_INCREASE_MANUALLY"),
        });
      }
      userRequestObj.leave_days = leave_days_obj ? await calculateLeaveTotal(leave_days_obj, findGeneralSetting?.leave_period_type) : leave_days ? leave_days : findLeaveRequest?.leave_days
      userRequestObj.leave_days_obj = leave_days_obj
    }
    if (userRemainingLeave > findLeaveRequest.leave_days) {
      if (userRemainingLeave - findLeaveRequest.leave_days < 0 && leavePolicyData[0]?.has_leave_unlimited != true && findGeneralSetting?.leave_calculation_type == request_calculation_type.AUTO) {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("FAIL_INCREASE_MANUALLY"),
        });
      }
    }

    const updateLeaveStatus = await UserRequest.setHeaders(req).update(
      userRequestObj,
      { where: { id: findLeaveRequest.id } },
    );

    if (updateLeaveStatus.length > 0) {

      // notification pending
      const templateData: any = {
        name: `${getRequestUserDetail.user_first_name} ${getRequestUserDetail.user_last_name}`,
        date: `${moment(findLeaveRequest.start_date).format("DD/MM/YYYY")} to ${moment(findLeaveRequest.end_date).format("DD/MM/YYYY")}`,
        admin: req.user.user_first_name,
        remark: remark,
        ORGANIZATION_LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        ADDRESS: EMAIL_ADDRESS.ADDRESS,
        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
        EMAIL: EMAIL_ADDRESS.EMAIL,
        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
        smtpConfig: 'INFO',
        mail_type: 'leave_action',
      };
      const employeeName = [];

      if (getUserDetail?.user_first_name) {
        employeeName.push(getUserDetail.user_first_name);
      }
      if (getUserDetail?.user_middle_name) {
        employeeName.push(getUserDetail.user_middle_name);
      }
      if (getUserDetail?.user_last_name) {
        employeeName.push(getUserDetail.user_last_name);
      }

      if (action == request_status.APPROVED) {
        templateData.request_status = request_status.APPROVED;
        templateData.email = getRequestUserDetail.user_email
        templateData.action = request_status.APPROVED.charAt(0).toUpperCase() +
          request_status.APPROVED.slice(1),
          await createNotification([getRequestUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.LEAVE_RESPONSE.content(employeeName.join(' '), action), NOTIFICATIONCONSTANT.LEAVE_RESPONSE.heading, REDIRECTION_TYPE.LEAVE, findLeaveRequest.id, { leave_id: findLeaveRequest.id })
        /** Send mail to user */
        await sendEmailNotification(templateData)

        /** If an employee takes leave during their resignation notice period, extend the notice period by the number of leave days taken. */
        /** check user is on notice period */
        const checkUserStatus = await Resignation.findOne({ where: { user_id: getRequestUserDetail.id, resignation_status: resignation_status.ACCEPTED }, attributes: ['id', 'last_serving_date'], raw: true })
        /** If user is on notice period then get user assigned policy for this request */
        if (checkUserStatus) {
          if (getUserPolicy) {
            /** get Tab-3 policy for check extend notice period scenario */
            const getLeavePolicy = await LeaveRestrictionPolicy.findOne({ where: { leave_accural_policy_id: getUserPolicy.user_leave_policy.id }, raw: true })
            if (getLeavePolicy) {
              if (getLeavePolicy.extend_notice_period == true) {
                const updateObj: any = {
                  last_serving_date: moment(checkUserStatus.last_serving_date).add(findLeaveRequest.leave_days, 'days')
                }
                await Resignation.update(updateObj, { where: { id: checkUserStatus.id } })
              }
            }
          }
        }

      } else if (action == request_status.REJECTED) {

        templateData.request_status = request_status.REJECTED;
        await createNotification([getRequestUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.LEAVE_RESPONSE.content(employeeName.join(' '), action), NOTIFICATIONCONSTANT.LEAVE_RESPONSE.heading, REDIRECTION_TYPE.LEAVE, findLeaveRequest.id, { leave_id: findLeaveRequest.id })
        templateData.email = getRequestUserDetail.user_email
        templateData.action = request_status.REJECTED.charAt(0).toUpperCase() +
          request_status.REJECTED.slice(1),
          /** Send mail to user */
          await sendEmailNotification(templateData)
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_USER_REQUEST_UPDATE"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_USER_REQUEST_NOT_UPDATE"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Create or update leavetype
 * @param req
 * @param res
 * @returns
 */

export const createOrUpdateLeaveType = async (req: Request, res: Response) => {
  const { name, remark, status, has_annual_leave, leave_deduction_type, user_probation_status, leave_type_color, leave_period_type } = req.body;
  const { id } = req.params;

  try {
    // Check if a record with the same name already exists
    const existingLeaveType = await LeaveTypeModel.findOne({ where: { name, status: { [Op.not]: leaveTypeStatus.DELETED }, organization_id: req.user.organization_id }, raw: true });
    if (existingLeaveType && !id) {
      return res.status(StatusCodes.CONFLICT).json({ status: false, message: res.__("LEAVE_TYPE_DUPLICATION_ERROR") });
    }
    if (existingLeaveType && id && existingLeaveType.id !== Number(id)) {
      return res.status(StatusCodes.CONFLICT).json({ status: false, message: res.__("LEAVE_TYPE_DUPLICATION_ERROR") });
    }
    // else if (existingLeaveType && !id) {
    //   if (existingLeaveType.status != leaveTypeStatus.ACTIVE) {
    //     await LeaveTypeModel.setHeaders(req).update({ status: leaveTypeStatus.ACTIVE, updated_by: req.user.id }, { where: { id: existingLeaveType.id } })
    //     return res.status(StatusCodes.CREATED).json({ status: true, message: res.__("LEAVE_TYPE_CREATION_SUCCESS") });
    //   }
    // }

    let leaveType;

    if (id) {
      // Update existing leave type
      leaveType = await LeaveTypeModel.findByPk(id);
      if (!leaveType) {
        return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('LEAVE_TYPE_NOT_FOUND') });
      }
      const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
      const updateObj: any = {
        name: name,
        remark: remark,
        updated_by: req.user.id,
        status: status ? status : leaveTypeStatus.ACTIVE
      }
      if (has_annual_leave) {
        updateObj.has_annual_leave = has_annual_leave
      }
      if (leave_deduction_type) {
        updateObj.leave_deduction_type = leave_deduction_type
      }
      if (user_probation_status) {
        updateObj.user_probation_status = user_probation_status
      }
      if (leave_type_color) {
        updateObj.leave_type_color = leave_type_color
      }
      if (leave_period_type) {
        updateObj.leave_period_type = findGeneralSetting?.leave_period_type
      }

      await LeaveTypeModel.setHeaders(req).update(updateObj, { where: { id: leaveType.id, organization_id: req.user.organization_id } })
    } else {
      const findAnnualLeave = await LeaveTypeModel.findOne({ where: { has_annual_leave: true, status: leaveTypeStatus.ACTIVE, organization_id: req.user.organization_id }, raw: true })
      // Create new leave type
      leaveType = await LeaveTypeModel.setHeaders(req).create({ name, status: status ? status : leaveTypeStatus.ACTIVE, remark, has_annual_leave: findAnnualLeave ? false : true, leave_deduction_type, user_probation_status, leave_type_color, leave_period_type, created_by: req.user.id, updated_by: req.user.id, organization_id: req.user.organization_id } as any);
    }

    return res.status(StatusCodes.CREATED).json({ status: true, message: id ? res.__("LEAVE_TYPE_UPDATION_SUCCESS") : res.__("LEAVE_TYPE_CREATION_SUCCESS") });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * @param req
 * @param res
 * @returns
 */
// Get List of Leave Types with Search and Filter
export const getLeaveTypes = async (req: Request, res: Response) => {
  const { search, status, page, size, id } = req.query;
  const { limit, offset } = getPagination(Number(page), Number(size));
  try {
    // Generate role-based user filtering conditions
    const roleBasedUserFilter = await generateRoleBasedUserFilter(req);

    const whereClause: any = {
      where: {
        // status: leaveTypeStatus.ACTIVE
        organization_id: req.user.organization_id
      },
      include: [{
        model: LeaveAccuralPolicy,
        as: 'leave_accural_policy',
        attributes: ['id', 'leave_policy_name', 'has_leave_policy_default', [
          sequelize.literal(`(
                                SELECT COUNT(*)
                                FROM nv_user_leave_policy AS ulp
                                INNER JOIN nv_users AS u ON ulp.user_id = u.id
                                WHERE ulp.leave_accural_policy_id = leave_accural_policy.id
                                AND ulp.user_leave_policy_status = '${user_leave_policy_status.ACTIVE}'
                                AND u.user_status NOT IN ('deleted', 'cancelled')
                                ${roleBasedUserFilter}
                            )`),
          'leave_policy_user_count'
        ],
          [
            sequelize.literal(`(
                                SELECT GROUP_CONCAT(ulp.user_id)
                                FROM nv_user_leave_policy AS ulp
                                INNER JOIN nv_users AS u ON ulp.user_id = u.id
                                WHERE ulp.leave_accural_policy_id = leave_accural_policy.id
                                AND ulp.user_leave_policy_status = '${user_leave_policy_status.ACTIVE}'
                                AND u.user_status NOT IN ('deleted', 'cancelled')
                                ${roleBasedUserFilter}
                            )`),
            'leave_policy_user_ids'
          ]
        ],
        where: { status: leave_accural_status.ACTIVE },
        required: false
      }]
    };
    if (id) {
      whereClause.where.id = id;
    }
    if (search) {
      whereClause.where.name = { [Op.like]: `%${search}%` };
    }

    if (status) {
      whereClause.where.status = status;
    } else {
      whereClause.where.status = { [Op.not]: leaveTypeStatus.DELETED }
    }

    if (page && size) {
      whereClause.limit = Number(limit)
      whereClause.offset = Number(offset)
    }


    const leaveTypes = await LeaveTypeModel.findAndCountAll(whereClause);
    const count = await LeaveTypeModel.count({ where: whereClause.where })
    const response = getPaginatedItems(Number(size), Number(page), count);

    const data = {
      data: await Promise.all(leaveTypes.rows?.map(async (leave_type: any) => {
        if (leave_type.created_by) {
          leave_type.created_by = await User.findOne({
            attributes: ['id', 'user_status', [
              sequelize.literal(
                `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
              ),
              "user_avatar_link",
            ], [
                sequelize.fn(
                  "concat",
                  sequelize.col("user_first_name"),
                  " ",
                  sequelize.col("user_last_name"),
                ),
                "user_full_name",
              ]], where: { id: leave_type.created_by }
          })
        }
        return leave_type
      })),
      ...response
    }
    const findAnnualExist = await LeaveTypeModel.findOne({ where: { has_annual_leave: true, status: leaveTypeStatus.ACTIVE, organization_id: req.user.organization_id }, raw: true })
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      ...data,
      anuualExist: findAnnualExist ? true : false
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * @param req
 * @param res
 * @returns
 */
// Delete Leave Type
export const deleteLeaveType = async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const leaveType = await LeaveTypeModel.findByPk(id);
    if (!leaveType) {
      return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('LEAVE_TYPE_NOT_FOUND') });
    }

    // Check if the leave type is in use
    // (Assume we have a relationship that can be checked here, e.g., LeavePolicyRelationModel)
    const isInUse = await LeaveAccuralPolicy.count({ where: { leave_type_id: id, status: user_leave_policy_status.ACTIVE } });

    if (isInUse) {
      return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__('LEAVE_TYPE_DELETION_FAILED_IN_USE') });
    }


    await LeaveTypeModel.setHeaders(req).update({ status: leaveTypeStatus.DELETED }, { where: { id: id } });
    res.status(StatusCodes.OK).json({ status: true, message: res.__('LEAVE_TYPE_DELETION_SUCCESS') });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Create or Update Leave Policy with Multiple Leave Types
export const createOrUpdateLeavePolicy = async (req: Request, res: Response) => {
  const { name, remark, leave_types, status } = req.body;
  const { id } = req.params;

  const transaction = await sequelize.transaction();

  try {
    // Check if a record with the same name already exists
    const existingLeavePolicy = await LeavePolicyModel.findOne({ where: { name } });

    if (existingLeavePolicy && id && existingLeavePolicy.id !== Number(id)) {
      await transaction.rollback();
      return res.status(StatusCodes.CONFLICT).json({ status: false, message: res.__('LEAVE_POLICY_DUPLICATION_ERROR') });
    } else if (existingLeavePolicy && !id) {
      if (existingLeavePolicy.status != leavePolicyStatus.ACTIVE) {
        const updateObj: any = {
          name: name,
          status: status ? status : leavePolicyStatus.ACTIVE,
          remark: remark,
          updated_by: req.user.id,
        }
        await LeavePolicyModel.setHeaders(req).update(updateObj, { where: { id: existingLeavePolicy.id }, transaction });

        // Delete old LeavePolicyTypeJoin records
        await LeavePolicyRelationModel.destroy({ where: { policy_id: existingLeavePolicy.id }, transaction });

        const joinRecords = leave_types.map((lt: { type_id: number, days: number, duration_type: string, has_unlimited: boolean }) => ({
          policy_id: existingLeavePolicy.id,
          type_id: lt.type_id,
          days: lt.days,
          duration_type: lt.duration_type,
          status: status ? status : leaveRelationStatus.ACTIVE,
          has_unlimited: lt.has_unlimited,
          created_by: req.user.id, updated_by: req.user.id
        }));

        await LeavePolicyRelationModel.setHeaders(req).bulkCreate(joinRecords, { transaction });

        await transaction.commit();

        return res.status(StatusCodes.CREATED).json({ status: true, message: res.__("LEAVE_POLICY_CREATION_SUCCESS") });
      }
    }

    let leavePolicy: any;

    if (id) {
      // Update existing leave policy
      leavePolicy = await LeavePolicyModel.findByPk(id);
      if (!leavePolicy) {
        await transaction.rollback();
        return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('LEAVE_POLICY_NOT_FOUND') });
      }
      const updateObj: any = {
        name: name,
        status: status ? status : leavePolicyStatus.ACTIVE,
        remark: remark,
        updated_by: req.user.id,
      }
      await LeavePolicyModel.setHeaders(req).update(updateObj, { where: { id: id }, transaction });

      // Delete old LeavePolicyTypeJoin records
      await LeavePolicyRelationModel.destroy({ where: { policy_id: id }, transaction });
    } else {
      // Create new leave policy
      leavePolicy = await LeavePolicyModel.setHeaders(req).create({ name, status: status ? status : leavePolicyStatus.ACTIVE, remark, created_by: req.user.id, updated_by: req.user.id } as any, { transaction });
    }

    // Create LeavePolicyTypeJoin records
    const joinRecords = leave_types.map((lt: { type_id: number, days: number, duration_type: string, has_unlimited: boolean }) => ({
      policy_id: leavePolicy.id,
      type_id: lt.type_id,
      days: lt.days,
      duration_type: lt.duration_type,
      status: leaveRelationStatus.ACTIVE,
      has_unlimited: lt.has_unlimited,
      created_by: req.user.id, updated_by: req.user.id
    }));
    await LeavePolicyRelationModel.setHeaders(req).bulkCreate(joinRecords, { transaction });

    await transaction.commit();
    return res.status(StatusCodes.CREATED).json({ status: true, message: id ? res.__("LEAVE_POLICY_UPDATION_SUCCESS") : res.__("LEAVE_POLICY_CREATION_SUCCESS") });
  } catch (error) {
    await transaction.rollback();
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Get List of Leave Policies with Search and Filter
export const getLeavePolicies = async (req: Request, res: Response) => {
  const { search, status, page, size, id } = req.query;
  const { limit, offset } = getPagination(Number(page), Number(size));

  try {

    const findOrgLeaveType = await LeaveTypeModel.findAll({ where: { organization_id: req.user.organization_id } })

    if (findOrgLeaveType.length == 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__('LEAVE_TYPE_NOT_FOUND') });
    }

    const whereClause: any = {
      where: {
        // status: leavePolicyStatus.ACTIVE
        leave_type_id: findOrgLeaveType.map((item: any) => item.id)
      },
    };



    if (page && size) {
      whereClause.limit = Number(limit)
      whereClause.offset = Number(offset)
    }

    if (id) {
      whereClause.where.id = id;
    }
    if (search) {
      whereClause.where.name = { [Op.like]: `%${search}%` };
    }

    if (status) {
      whereClause.where.status = status;
    } else {
      whereClause.where.status = { [Op.not]: leavePolicyStatus.DELETED };
    }

    const leavePolicies = await LeavePolicyModel.findAndCountAll(whereClause);
    const response = getPaginatedItems(Number(size), Number(page), leavePolicies?.count);

    const data = {
      data: await Promise.all(leavePolicies?.rows?.map(async (policy) => {
        const clonePolicy = JSON.parse(JSON.stringify(policy))
        if (policy.created_by) {
          const findUser = await User.findOne({
            attributes: ['id', 'user_status', [
              sequelize.literal(
                `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
              ),
              "user_avatar_link",
            ], [
                sequelize.fn(
                  "concat",
                  sequelize.col("user_first_name"),
                  " ",
                  sequelize.col("user_last_name"),
                ),
                "user_full_name",
              ]], where: { id: policy.created_by }
          })
          clonePolicy.created_by = findUser ? findUser : policy.created_by
        }

        const findLeaveTypes = await LeaveTypeModel.findAll({
          attributes: ['id', 'name', [sequelize.literal(`(select days from nv_leave_policy_relation where policy_id=${policy?.id} and type_id=LeaveTypeModel.id)`), 'days'], [sequelize.literal(`(select duration_type	 from nv_leave_policy_relation where policy_id=${policy?.id} and type_id=LeaveTypeModel.id)`), 'duration_type'], [sequelize.literal(`(select has_unlimited	 from nv_leave_policy_relation where policy_id=${policy?.id} and type_id=LeaveTypeModel.id)`), 'has_unlimited'], 'status', 'createdAt'],
          where: {
            id: { [Op.in]: [sequelize.literal(`(SELECT type_id from nv_leave_policy_relation where policy_id=${policy?.id})`)] },
            status: leaveTypeStatus.ACTIVE
          }
        })
        if (findLeaveTypes.length) {
          clonePolicy.leave_types = findLeaveTypes
        }
        return clonePolicy
      })),
      ...response
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      ...data,
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Delete Leave Policy
export const deleteLeavePolicy = async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const leavePolicy = await LeavePolicyModel.findByPk(id);
    if (!leavePolicy) {
      return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('LEAVE_POLICY_NOT_FOUND') });
    }

    // Check if the leave policy is in use
    const isInUse = await UserMeta.count({ where: { leave_policy_id: id } });

    if (isInUse) {
      return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__('LEAVE_POLICY_DELETION_FAILED_ASSIGNED') });
    }

    await LeavePolicyModel.setHeaders(req).update({ status: leavePolicyStatus.DELETED }, { where: { id: id } })
    res.status(200).json({ status: true, message: res.__('LEAVE_POLICY_DELETION_SUCCESS') });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserLeaveListTimeWise = async (req: any, res: any) => {
  try {
    const { list_type } = req.query;
    const result = await getLeaveRequestsByDateRanges(req, 'dashboard', list_type, moment().toDate(), moment().toDate());
    res.status(200).json(result);
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const cancelLeaveRequest = async (req: any, res: any) => {
  try {
    const { remark, request_id, leave_days, leave_days_obj } = req.body;
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'user_first_name', 'user_last_name', 'user_middle_name', 'user_email', 'organization_id', 'web_user_active_role_id', 'user_active_role_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({ where: { id: req.headers["platform-type"] == "web" ? getUserDetail?.web_user_active_role_id : getUserDetail?.user_active_role_id, role_status: role_status.ACTIVE }, raw: true })


    const findLeaveRequest = await UserRequest.findOne({
      where: { id: request_id }, raw: true
    });
    const isApprovedRequest = findLeaveRequest?.request_status == request_status.APPROVED;

    const userHasPrivilegedRole = [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
      ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER
    ].includes(getUserCurrentRole?.role_name);
    const isRequestingUser = req.user.id == findLeaveRequest?.from_user_id;

    /** commented code as per flow, user can cancelled their own leave request */
    // if (isRequestingUser) {
    //   return res.status(StatusCodes.BAD_REQUEST).json({
    //     status: false,
    //     message: res.__("LEAVE_REQUEST_CANCELED_FAILED"),
    //   });
    // }

    if (isApprovedRequest && !userHasPrivilegedRole && isRequestingUser) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_CANCEL_APPROVED_REQUEST"),
      });
    }
    const getRequestUserDetail: any = await User.findOne({
      attributes: ['id', 'user_first_name', 'user_last_name', 'user_middle_name', 'user_email', 'appToken', 'webAppToken'],
      where: { id: findLeaveRequest?.from_user_id, organization_id: req.user.organization_id }, raw: true
    });
    if (!findLeaveRequest) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_REQUEST_NOT_FOUND"),
      });
    }

    const userRequestObj: any = {
      request_status: request_status.CANCELLED,
      approved_by_reason: remark,
      request_approved_by: req.user.id,
      updated_by: req.user.id,
    }
    const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
    const leaveYear = moment(findLeaveRequest?.start_date).year()
    const leavePolicyData = (await getUserLeaveBalanceFunc(findLeaveRequest?.from_user_id, leaveYear, null, findLeaveRequest.leave_request_type, findLeaveRequest?.leave_request_type, null, false, findGeneralSetting?.leave_period_type, req.user.organization_id)).data

    let leaveHour;

    if (findGeneralSetting?.leave_period_type === 'day') {
      const totalDays = leave_days_obj
        ? await calculateLeaveTotal(leave_days_obj, 'day')
        : leave_days;
      leaveHour = totalDays;
    } else if (findGeneralSetting?.leave_period_type === 'hour') {
      const totalHours = leave_days_obj
        ? await calculateLeaveTotal(leave_days_obj, 'hour')
        : leave_days;
      leaveHour = totalHours
    } else {
      leaveHour = leave_days; // fallback if type is not day/hour
    }
    const userRemainingLeave = leavePolicyData[0]?.user_remaining_leave //
    const findDiffrence = leaveHour - findLeaveRequest.leave_days
    if (findGeneralSetting?.leave_calculation_type == request_calculation_type.MANUAL) {
      if (findDiffrence > 0) {
        userRemainingLeave - findDiffrence
      }
      if (userRemainingLeave < leaveHour) {
        return { status: false, message: res.__('FAIL_INCREASE_MANUALLY') }
      }
      userRequestObj.leave_days = leave_days_obj ? await calculateLeaveTotal(leave_days_obj, findGeneralSetting?.leave_period_type) : leave_days ? leave_days : findLeaveRequest?.leave_days
      userRequestObj.leave_days_obj = leave_days_obj
    }

    const updateLeaveStatus = await UserRequest.setHeaders(req).update(
      userRequestObj,
      { where: { id: findLeaveRequest.id } },
    );

    if (updateLeaveStatus.length > 0) {

      const requestEmployeeName = [];

      if (getRequestUserDetail?.user_first_name) {
        requestEmployeeName.push(getRequestUserDetail.user_first_name);
      }
      if (getRequestUserDetail?.user_middle_name) {
        requestEmployeeName.push(getRequestUserDetail.user_middle_name);
      }
      if (getRequestUserDetail?.user_last_name) {
        requestEmployeeName.push(getRequestUserDetail.user_last_name);
      }
      // notification pending
      const templateData: any = {
        name: requestEmployeeName.join(' '),
        date: `${moment(findLeaveRequest.start_date).format("DD/MM/YYYY")} to ${moment(findLeaveRequest.end_date).format("DD/MM/YYYY")}`,
        admin: req.user.user_first_name,
        remark: remark,
        request_status: request_status.CANCELLED,
        ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        ADDRESS: EMAIL_ADDRESS.ADDRESS,
        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
        EMAIL: EMAIL_ADDRESS.EMAIL,
        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
        smtpConfig: 'INFO'
      };
      const employeeName = [];

      if (getUserDetail?.user_first_name) {
        employeeName.push(getUserDetail.user_first_name);
      }
      if (getUserDetail?.user_middle_name) {
        employeeName.push(getUserDetail.user_middle_name);
      }
      if (getUserDetail?.user_last_name) {
        employeeName.push(getUserDetail.user_last_name);
      }

      if (userHasPrivilegedRole) {
        await createNotification([getRequestUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.LEAVE_RESPONSE.content(employeeName.join(' '), request_status.CANCELLED), NOTIFICATIONCONSTANT.LEAVE_RESPONSE.heading, REDIRECTION_TYPE.LEAVE, findLeaveRequest.id, { leave_id: findLeaveRequest.id })
        templateData.email = getRequestUserDetail.user_email
        await sendEmailNotification(templateData)
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("LEAVE_REQUEST_CANCELED_SUCCESSFULLY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("LEAVE_REQUEST_CANCELED_FAILED"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const LeaveRequestCount = async (req: any, res: any) => {
  try {
    const { user_id } = req.params;

    const leaveTypes: any = await LeaveTypeModel.findAll({ where: { organization_id: req.user.organization_id }, raw: true });
    const leaveTypeData = await Promise.all(
      leaveTypes.map(async (leaveType: any) => {
        const leaveObj = {
          balance: 0,
          used_leave_balance: 0,
          leave_days: 0,
        };

        const findUserLeave: any = await UserMeta.findOne({
          attributes: ['leave_days'],
          where: {
            user_id: user_id,
            leave_type_id: leaveType.id,
          },
        });

        if (findUserLeave) {
          const getLeaveBalance = await fetchUserLeavePolicies(user_id, leaveType.id, findUserLeave.leave_duration_type);
          leaveObj.balance = getLeaveBalance[0].balance || 0;
          leaveObj.used_leave_balance = getLeaveBalance[0].balance - findUserLeave.leave_days;
          leaveObj.leave_days = findUserLeave.leave_days || 0;
        }

        return {
          ...leaveType,
          leave_records: leaveObj,
        };
      })
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: leaveTypeData
    });
  } catch (error) {
    console.error("Error fetching leave request count:", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getCalenderWiseLeaveList = async (req: any, res: any) => {
  try {
    const { months, year, search, start_date, end_date, list_type }: any = req.query;
    const leave_start_date = start_date ? moment(start_date).toDate() : moment().toDate();
    const leave_end_date = end_date ? moment(end_date).toDate() : moment().toDate();
    const monthArray = months ? months.split(',').map((m: string) => parseInt(m.trim())) : [];
    const result: any = await getLeaveRequestsByDateRanges(req, 'calender', list_type, leave_start_date, leave_end_date, monthArray, parseInt(year), search);
    res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: {
        calenderLeaves: result?.calenderLeaves.length ? result?.calenderLeaves : [],
        holidayList: result?.holidayList.length ? result?.holidayList : []
      }
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getLeaveDetailById = async (req: any, res: any) => {
  try {
    const { leave_id } = req.params;
    const getLeaveData = await UserRequest.findOne({
      include: [
        {
          model: User,
          as: "request_from_users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_from_users.user_first_name"),
                " ",
                sequelize.col("request_from_users.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            [
              sequelize.literal(
                `CASE 
            WHEN request_from_users.user_avatar IS NULL OR request_from_users.user_avatar = '' THEN ''
            WHEN NOT request_from_users.user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = request_from_users.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', request_from_users.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = request_from_users.user_avatar))
          END`
              ),
              "user_avatar_link",
            ],
            "employment_number"
          ],
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: { branch_status: branch_status.ACTIVE },
              required: false,
            },
            {
              model: Department,
              as: "department",
              attributes: ["department_name"],
              where: { department_status: department_status.ACTIVE },
              required: false,
            },
            {
              model: Role,
              as: "role",
              attributes: ["role_name"],
              where: { role_status: role_status.ACTIVE },
              required: false,
            },
            {
              model: UserWeekDay,
              as: "user_week_day",
              attributes: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
              where: { user_weekday_status: user_weekday_status.ACTIVE, user_id: sequelize.col("request_from_users.id") },
              required: false,
            },

          ],
          required: true,
        },
        {
          model: User,
          as: "request_approved_users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_approved_users.user_first_name"),
                " ",
                sequelize.col("request_approved_users.user_last_name"),
              ),
              "user_full_name",
            ],
            "employment_number"
          ],
        },
        {
          model: MORole,
          as: "leave_role_request",
          attributes: ["id", "role_name"]
        },
        {
          model: LeaveTypeModel,
          as: "leave_request_type_list",
          attributes: ['id', 'name', 'leave_deduction_type'],
          required: false
        }
      ],
      where: {
        id: leave_id
      }
    })
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getLeaveData || {}
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getLeaveRuleList = async (req: any, res: any) => {
  try {
    const getLeaveRuleList = await LeaveRule.findAll({ where: { leave_rule_status: leave_rule_status.ACTIVE } });
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getLeaveRuleList.length ? getLeaveRuleList : []
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const modifyLeaveRule = async (req: any, res: any) => {
  try {
    const { leave_rule_list = [] } = req.body;
    if (leave_rule_list.length > 0) {
      for (const leave_rule of leave_rule_list) {
        await LeaveRule.setHeaders(req).update({ leave_rule_boolean: leave_rule.leave_rule_boolean, leave_rule_days: leave_rule.leave_rule_days, leave_rule_start_date: leave_rule.leave_rule_start_date, leave_rule_end_date: leave_rule.leave_rule_end_date }, { where: { id: leave_rule.leave_rule_id } });
      }
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("LEAVE_RULE_UPDATED_SUCCESSFULLY")
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getLeaveReportList = async (req: any, res: any) => {
  try {
    const { branch_id, department_id, role_id, search, page, size, download, leave_year, start_date, end_date, report_mode, leave_type_id, type } = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id', [
        sequelize.fn(
          "concat",
          sequelize.col("user_first_name"),
          " ",
          sequelize.col("user_last_name"),
        ),
        "user_full_name",
      ]],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const findUserRole: any = await Role.findOne({
      where: { id: getUserDetail.web_user_active_role_id }, raw: true
    });

    /** Get all super admin user id's */
    const getUserId = await getSuperAdminUserIdEnhanced(req.user.organization_id);
    const excludedIds = [...getUserId]; // Combine req.user.id with fetched IDs
    const whereObj: any = {
      user_status: {
        [Op.not]: [
          user_status.CANCELLED,
          user_status.DELETED,
        ],
      },
      id: {
        [Op.not]: excludedIds,
      },
      organization_id: req.user.organization_id
    }

    if (branch_id) {
      whereObj.branch_id = branch_id
    }
    if (department_id) {
      whereObj.department_id = department_id
    }

    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        Sequelize.where(
          Sequelize.col("user_email"),
          {
            [Op.like]: `%${search}%`,
          },
        )
      ];
    }

    if (
      findUserRole &&
      (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER || findUserRole.role_name == ROLE_CONSTANT.SIGNATURE) &&
      getUserDetail.branch_id
    ) {
      whereObj.branch_id = getUserDetail.branch_id;

      // Handle role hierarchy for both MORole and old role systems
      if (getUserDetail.user_role_id) {
        // Use MORole system - construct recursive query for mo_roles
        const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM mo_roles
            WHERE id = :activeRoleId AND organization_id = :organizationId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM mo_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
            WHERE r.organization_id = :organizationId
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId`;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
          replacements: {
            activeRoleId: getUserDetail.user_role_id,
            organizationId: getUserDetail.organization_id
          },
          type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles (MORole system)
        let whereStr = '';
        getChildRoles.forEach((child_role: any, index: number) => {
          if (index > 0) {
            whereStr += ' OR ';
          }
          whereStr += `User.user_role_id = ${child_role.id}`;
        });
        if (whereStr) {
          // Initialize Op.and array if not present
          if (!whereObj[Op.and]) {
            whereObj[Op.and] = [];
          }
          // Add the generated SQL condition
          whereStr += ` OR User.id=${req.user.id}`;
          whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
        }
      } else {
        // Fallback to old role system
        const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM nv_roles
            WHERE id = :activeRoleId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM nv_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId`;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
          replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
          type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles (old system)
        let whereStr = '';
        getChildRoles.forEach((child_role: any, index: number) => {
          if (index > 0) {
            whereStr += ' OR ';
          }
          whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
        });
        if (whereStr) {
          // Initialize Op.and array if not present
          if (!whereObj[Op.and]) {
            whereObj[Op.and] = [];
          }
          // Add the generated SQL condition
          whereStr += ` OR User.id=${req.user.id}`;
          whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
        }
      }
    }
    const getUserListQuery: any = {
      where: whereObj,
      attributes: ["id",
        [
          sequelize.fn(
            "concat",
            sequelize.col("user_first_name"),
            " ",
            sequelize.col("user_last_name"),
          ),
          "user_full_name",
        ],
        "user_joining_date",
        "user_status",
        "user_email",
        "employment_number",
        [
          sequelize.literal(
            `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
          ),
          "user_avatar_link",
        ],
        "user_avatar",
        [
          sequelize.literal(
            `COALESCE((select leave_days from nv_user_meta where nv_user_meta.user_id=User.id), 0)`
          ),
          "total_leave_days",
        ],
      ],
      include: [
        {
          model: Branch,
          as: "branch",
          attributes: ["id", "branch_name", "branch_color", "text_color"]
        },
        {
          model: Department,
          as: "department",
          attributes: ["id", "department_name"]
        }
      ],
      raw: true,
      nest: true
    }

    if (page && size) {
      getUserListQuery.limit = Number(limit);
      getUserListQuery.offset = Number(offset);
    }

    if (role_id) {
      whereObj[Op.and] = [
        sequelize.literal(`(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id = ${role_id} AND role_id NOT IN (1))) `)
      ]
    } else {
      whereObj[Op.and] = [
        sequelize.literal(`(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1))) `)
      ]
    }

    const getUserList: any = await User.findAll(getUserListQuery)

    const finalUserList = [];
    if (getUserList.length > 0) {
      for (const user of getUserList) {
        const leavePolicyData = (await getUserLeaveBalanceFunc(user.id, leave_year, null, leave_type_id, start_date, end_date, true, report_mode, req.user.organization_id)).data
        // Filter: Exclude user if leave_type_id is passed and no matching policy is found
        if (leave_type_id && (!leavePolicyData || leavePolicyData.length === 0)) {
          continue;
        }

        // When type=report, check if user has leave requests in the specified date range
        if (type === 'report' && start_date && end_date) {
          const hasLeaveInDateRange = await UserRequest.findOne({
            where: {
              from_user_id: user.id,
              request_status: {
                [Op.in]: [request_status.APPROVED, request_status.PENDING],
              },
              [Op.and]: [
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("start_date")),
                  {
                    [Op.lte]: end_date,
                  },
                ),
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("end_date")),
                  {
                    [Op.gte]: start_date,
                  },
                ),
              ],
            },
            raw: true,
          });

          // Skip user if they don't have any leave requests in the date range
          if (!hasLeaveInDateRange) {
            continue;
          }
        }
        const totals = leavePolicyData.length > 0 ? leavePolicyData.reduce((acc: any, item: any) => {
          acc.leave_balance += formatNumber(item.leave_balance) || 0;
          acc.user_remaining_leave += formatNumber(item.user_remaining_leave) || 0;
          acc.planned_leaves += formatNumber(item.used_leave?.planned_leaves) || 0;
          acc.used_leaves += formatNumber(item.used_leave?.used_leaves) || 0;
          acc.total_user_leave += formatNumber(item.used_leave?.total) || 0;
          return acc;
        }, {
          leave_balance: 0,
          user_remaining_leave: 0,
          planned_leaves: 0,
          used_leaves: 0,
          total_user_leave: 0
        }) : {}
        user.leave_details = leavePolicyData
        user.leave_details_total = totals
        user.available_balance = formatNumber(totals?.user_remaining_leave) || 0
        user.leave_balance = formatNumber(totals?.leave_balance) || 0
        user.total_used_leave_days = formatNumber(totals?.total_user_leave) || 0
        user.total_leave_days = formatNumber(totals?.leave_balance) || 0

        finalUserList.push(user);
      }
    }


    if (download) {
      if (download == 'excel') {
        const workbook: any = await generateFile(download)
        const worksheet = workbook.addWorksheet('User Data');

        // Define header priority
        const headerPriority = [
          'employment_number',
          'user_full_name',
          'user_email',
          'user_status',
          'branch',
          'department',
          'total_leave_days',
          'total_used_leave_days',
          'available_balance'
        ]; // Order of headers



        // Add title and metadata
        const titleRow = worksheet.addRow(['User leave report']);

        // Get the total number of columns dynamically
        const totalColumns = headerPriority.length;
        const lastColumnLetter = String.fromCharCode(64 + totalColumns); // Convert number to Excel column letter

        // Merge title row across all columns
        worksheet.mergeCells(`A1:${lastColumnLetter}1`);

        // Style the title row
        titleRow.eachCell((cell: any) => {
          cell.font = { bold: true, size: 14 };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });

        if (branch_id) {
          const getBranchData: any = await Branch.findOne({ where: { id: branch_id, organization_id: req.user.organization_id }, raw: true });
          worksheet.addRow(['Branch:', getBranchData.branch_name]);
        }

        if (department_id) {
          const getDepartmentData: any = await Department.findOne({ where: { id: department_id, organization_id: req.user.organization_id }, raw: true });
          worksheet.addRow(['Department :', getDepartmentData.department_name]);
        }

        if (search) {
          worksheet.addRow(['Search:', search]);
        }

        if (role_id) {
          const getRoleData: any = await Role.findOne({ where: { id: role_id }, raw: true });
          worksheet.addRow(['Role:', getRoleData.role_name]);
        }

        worksheet.addRow(['Report Date:', moment().format('DD MMMM YYYY, h:mm A')]);
        worksheet.addRow([]); // Empty row for spacing

        // Filter headers: Exclude unwanted keys and keep the ones in priority order
        const headers = headerPriority.filter(key => Object.keys(getUserList[0]).includes(key));


        // Add headers dynamically after metadata rows
        const headerRow = worksheet.addRow(headers.map(header => header.replace(/_/g, ' ').toUpperCase()));

        // Apply styling to the dynamically determined header row
        headerRow.eachCell((cell: any) => {
          cell.width = Math.max((cell.value || '').toString().length + 2, cell.width || 10);
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Dark blue background
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }; // White text
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.border = {
            top: { style: 'thin', color: { argb: '000000' } },
            left: { style: 'thin', color: { argb: '000000' } },
            bottom: { style: 'thin', color: { argb: '000000' } },
            right: { style: 'thin', color: { argb: '000000' } },
          };
        });

        // Define column keys
        worksheet.columns = headers.map((header) => ({
          key: header,
          width: 25,
        }));
        // Add filtered data rows
        getUserList.forEach((row: any) => {
          const filteredRow = headers.reduce((obj: any, key) => {
            if (key == 'branch') {
              obj[key] = row[key].branch_name;
            } else if (key == 'department') {
              obj[key] = row[key].department_name;
            } else {
              obj[key] = row[key]; // Pick only required fields
            }
            return obj;
          }, {});
          worksheet.addRow(filteredRow);
        });

        worksheet.eachRow((row: any) => {
          row.eachCell((cell: any, colNumber: any) => {
            const column = worksheet.getColumn(colNumber);
            column.width = Math.max((cell.value || '').toString().length + 2, column.width || 10);
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.border = {
              top: { style: 'thin', color: { argb: '000000' } },
              left: { style: 'thin', color: { argb: '000000' } },
              bottom: { style: 'thin', color: { argb: '000000' } },
              right: { style: 'thin', color: { argb: '000000' } },
            };
          });
        });
        // Send the workbook as a buffer
        const buffer = await workbook.xlsx.writeBuffer();
        res.setHeader('Content-Disposition', 'attachment; filename=report.xlsx');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(buffer);
      } else if (download == 'csv') {

        // Transform data (handle nested objects)
        const transformedData = getUserList.map((row: any) => ({
          "EMPLOYMENT NUMBER": row.employment_number,
          "USER FULL NAME": row.user_full_name,
          "USER EMAIL": row.user_email,
          "USER STATUS": row.user_status,
          "BRANCH": row.branch?.branch_name || '',   // Extract branch name
          "DEPARTMENT": row.department?.department_name || '',  // Extract department name
          "TOTAL LEAVE DAYS": row.total_leave_days,
          "TOTAL USED LEAVE DAYS": row.total_used_leave_days,
          "AVAILABLE BALANCE": row.available_balance,
        }));
        const buffer: any = await generateFile(download, transformedData)
        // Send the buffer in the response for file download
        res.setHeader('Content-Type', 'text/csv; charset=utf-8'); // Ensure the charset is specified
        res.setHeader('Content-Disposition', 'attachment; filename="report.csv"');
        res.setHeader('Content-Length', buffer.length.toString());
        res.end(buffer);
      }
      else if (download == 'pdf') {
        const [contentHtml, footerHtml] = await Promise.all([
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.USER_LEAVE_REPORT_PDF.template}.html`),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.FOOTER_TEMPLATE.template}.html`),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
        ]);

        const combinedTemplate = `${contentHtml}${footerHtml}`;
        try {

          const compiledTemplate = handlebars.compile(combinedTemplate);
          const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)

          const dataObj: any = {
            users: getUserList,
            current_date: moment().format('DD MMMM YYYY, h:mm A'),
            NAMASTE_LOGO: findGeneralSetting.brand_logo_link,
            GENERATED_BY_USER: getUserDetail.user_full_name,
            CONFIDENTIALITY_STATEMENT: EMAIL_ADDRESS.CONFIDENTIALITY_STATEMENT,
            MICROFFICE_LOGO_URL: global.config.API_UPLOAD_URL + "email_logo/logo.png"
          };
          if (branch_id) {
            const getBranchData: any = await Branch.findOne({ attributes: ['branch_name'], where: { id: branch_id, organization_id: req.user.organization_id }, raw: true });
            dataObj.branch = getBranchData.branch_name;
          }

          if (department_id) {
            const getDepartmentData: any = await Department.findOne({ attributes: ['department_name'], where: { id: department_id, organization_id: req.user.organization_id }, raw: true });
            dataObj.department = getDepartmentData.department_name;
          }

          if (role_id) {
            const getRoleData: any = await Role.findOne({ attributes: ['role_name'], where: { id: role_id } });
            dataObj.role = getRoleData.role_name;
          }

          if (search) {
            dataObj.search = search
          }

          const htmlToSend = compiledTemplate(dataObj);

          const pdfBuffer: any = await generateFile(download, htmlToSend)

          res.set({
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename=generated.pdf',
            'Content-Length': pdfBuffer.length
          });

          console.log("PDF generated and sent successfully!");
          res.send(pdfBuffer);
        } catch (e) {
          console.log("error", e);
        }
      }
    } else {
      delete getUserListQuery.attributes;
      // const getUserCount: any = await User.count({ where: whereObj });
      const { total_pages } = getPaginatedItems(
        size,
        page,
        finalUserList.length || 0,
      );

      return res.status(StatusCodes.OK).json({
        status: true,
        data: finalUserList,
        message: res.__("SUCCESS_FETCHED"),
        count: finalUserList.length,
        page: parseInt(page),
        size: parseInt(size),
        total_pages
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getLeaveTypeReport = async (req: any, res: any) => {
  try {
    const { download } = req.query

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;
    let start_date = req.query.start_date
    let end_date = req.query.end_date
    if (!start_date && !end_date) {
      start_date = `${currentYear}-${currentMonth}-01`
      end_date = `${currentYear}-${currentMonth}-31`
    }


    // Fetch all leave types
    let leaveTypes = await LeaveTypeModel.findAll({
      attributes: ['id', 'name', 'remark', 'leave_type_color', 'remark', 'leave_deduction_type',
        [
          Sequelize.literal(`
            (SELECT SUM(ulph.user_total_balance) AS user_total_balance
              FROM  nv_user_leave_policy AS ulp
              LEFT JOIN nv_user_leave_policy_history AS ulph ON ulph.leave_user_policy_id = ulp.id AND ulph.leave_accural_date BETWEEN '${start_date}' AND '${end_date}'
              WHERE
                  ulp.user_leave_policy_status = 'active' 
              AND ulp.leave_accural_policy_id IN( 
                SELECT id 
                  FROM nv_leave_accural_policy 
                  WHERE leave_type_id = LeaveTypeModel.id 
                  AND status = 'active'
              )
            )`
          ),
          'user_total_balance',
        ],
        [
          Sequelize.literal(`
            (SELECT SUM(ulph.total_used_leave) AS total_used_leave
              FROM  nv_user_leave_policy AS ulp
              LEFT JOIN nv_user_leave_policy_history AS ulph ON ulph.leave_user_policy_id = ulp.id AND ulph.leave_accural_date BETWEEN '${start_date}' AND '${end_date}'
              WHERE
                  ulp.user_leave_policy_status = 'active' 
              AND ulp.leave_accural_policy_id IN( 
                SELECT id 
                  FROM nv_leave_accural_policy 
                  WHERE leave_type_id = LeaveTypeModel.id 
                  AND status = 'active'
              )
            )`
          ),
          'total_used_leave',
        ],
      ],
      where: { status: { [Op.not]: leaveTypeStatus.DELETED }, organization_id: req.user.organization_id },
      include: [{
        model: LeaveAccuralPolicy,
        as: 'leave_accural_policy',
        attributes: ['id', 'leave_policy_name', 'has_leave_policy_default'],
        where: { status: leave_accural_status.ACTIVE },
        required: false
      }],
    });

    // Get allocated hours from user_meta table
    // const allocatedData = await UserMeta.findAll({
    //   attributes: [
    //     'leave_type_id',
    //     [Sequelize.fn('SUM', Sequelize.col('leave_days')), 'total_allocated_hours'],
    //   ],
    //   group: ['leave_type_id'],
    //   raw: true,
    // });
    // Get used hours from user_request table
    // const usedData = await UserRequest.findAll({
    //   attributes: [
    //     'leave_request_type',
    //     [Sequelize.fn('SUM', Sequelize.col('leave_days')), 'total_used_hours'],
    //   ],
    //   group: ['leave_request_type'],
    //   raw: true,
    // });
    // Map data to leave types
    leaveTypes = JSON.parse(JSON.stringify(leaveTypes))
    const reportData = leaveTypes.map((leaveType: any) => {
      // const allocated: any = allocatedData.find((a: any) => a.leave_type_id === leaveType.id);
      // const used: any = usedData.find((u: any) => u.leave_request_type === leaveType.id);

      // const totalAllocatedHours = allocated ? parseFloat(allocated.total_allocated_hours) : 0;
      // const totalUsedHours = used ? parseFloat(used.total_used_hours) : 0;

      // const percentageUsage: any = totalAllocatedHours > 0 ? (totalUsedHours / totalAllocatedHours) * 100 : 0;
      const percentageUsage: any = leaveType.user_total_balance > 0 ? ((Number(leaveType.total_used_leave) || 0) * 100) / (Number(leaveType.user_total_balance) || 0) : 0;
      return {
        id: leaveType.id,
        name: leaveType.name,
        total_allocated_leave: Number((leaveType.user_total_balance || 0).toFixed(2)), // totalAllocatedHours ? totalAllocatedHours.toFixed(2) : 0,
        total_percentage_usage: percentageUsage.toFixed(2) > 100 ? 100 : Number(percentageUsage.toFixed(2)), // in percentage
        total_hours_applied: Number((leaveType.total_used_leave || 0).toFixed(2)), //Number(totalUsedHours.toFixed(2)),
        leave_type_color: leaveType.leave_type_color,
        remark: leaveType.remark,
        leave_deduction_type: leaveType.leave_deduction_type,
        leave_policy: leaveType.leave_accural_policy
      };
    });
    if (download) {
      if (download == 'excel') {
        const workbook: any = await generateFile(download)
        const worksheet = workbook.addWorksheet('Leave Type Statictics');

        // Define header priority
        const headerPriority = [
          'id',
          'name',
          'total_allocated_leave',
          'total_percentage_usage',
          'total_hours_applied',
          'leave_deduction_type'
        ];

        // Add title and metadata
        const titleRow = worksheet.addRow(['Leave Type Statictics']);

        // Get the total number of columns dynamically
        const totalColumns = headerPriority.length;
        const lastColumnLetter = String.fromCharCode(64 + totalColumns); // Convert number to Excel column letter

        // Merge title row across all columns
        worksheet.mergeCells(`A1:${lastColumnLetter}1`);

        // Style the title row
        titleRow.eachCell((cell: any) => {
          cell.font = { bold: true, size: 14 };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });



        worksheet.addRow(['Report Date:', moment().format('DD MMMM YYYY, h:mm A')]);
        worksheet.addRow([]); // Empty row for spacing

        // Filter headers: Exclude unwanted keys and keep the ones in priority order
        const headers = headerPriority.filter(key => Object.keys(reportData[0]).includes(key));


        // Add headers dynamically after metadata rows
        const headerRow = worksheet.addRow(headers.map(header => header.replace(/_/g, ' ').toUpperCase()));

        // Apply styling to the dynamically determined header row
        headerRow.eachCell((cell: any) => {
          cell.width = Math.max((cell.value || '').toString().length + 2, cell.width || 10);
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Dark blue background
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }; // White text
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.border = {
            top: { style: 'thin', color: { argb: '000000' } },
            left: { style: 'thin', color: { argb: '000000' } },
            bottom: { style: 'thin', color: { argb: '000000' } },
            right: { style: 'thin', color: { argb: '000000' } },
          };
        });

        // Define column keys
        worksheet.columns = headers.map((header) => ({
          key: header,
          width: 25,
        }));
        // Add filtered data rows
        reportData.forEach((row: any) => {
          const filteredRow = headers.reduce((obj: any, key) => {
            obj[key] = row[key]; // Pick only required fields
            return obj;
          }, {});
          worksheet.addRow(filteredRow);
        });

        worksheet.eachRow((row: any) => {
          row.eachCell((cell: any, colNumber: any) => {
            const column = worksheet.getColumn(colNumber);
            column.width = Math.max((cell.value || '').toString().length + 2, column.width || 10);
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.border = {
              top: { style: 'thin', color: { argb: '000000' } },
              left: { style: 'thin', color: { argb: '000000' } },
              bottom: { style: 'thin', color: { argb: '000000' } },
              right: { style: 'thin', color: { argb: '000000' } },
            };
          });
        });
        // Send the workbook as a buffer
        const buffer = await workbook.xlsx.writeBuffer();
        res.setHeader('Content-Disposition', 'attachment; filename=report.xlsx');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(buffer);
      } else if (download == 'csv') {

        // Transform data (handle nested objects)
        const transformedData = reportData.map((row: any) => ({
          "ID": row.id,
          "NAME": row.name,
          "TOTAL ALLOCATED LEAVES": row.total_allocated_leave,
          "TOTAL PERCENTAGE USAGE": row.total_percentage_usage,
          "TOTAL HOURS APPLIED": row.total_hours_applied
        }));

        const buffer: any = await generateFile(download, transformedData)
        // Send the buffer in the response for file download
        res.setHeader('Content-Type', 'text/csv; charset=utf-8'); // Ensure the charset is specified
        res.setHeader('Content-Disposition', 'attachment; filename="report.csv"');
        res.setHeader('Content-Length', buffer.length.toString());
        res.end(buffer);
      }
      else if (download == 'pdf') {
        // Load HTML template
        const findUserDetail: any = await User.findOne({
          where: { id: req.user.id, organization_id: req.user.organization_id }, attributes: ['id', [
            sequelize.fn(
              "concat",
              sequelize.col("user_first_name"),
              " ",
              sequelize.col("user_last_name"),
            ),
            "user_full_name",
          ],], raw: true
        })

        const [contentHtml, footerHtml] = await Promise.all([
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.USER_LEAVE_CONSUMPTION_REPORT_PDF.template}.html`),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.FOOTER_TEMPLATE.template}.html`),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
        ]);

        const combinedTemplate = `${contentHtml}${footerHtml}`;
        try {
          const compiledTemplate = handlebars.compile(combinedTemplate);
          const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)

          const dataObj: any = {
            users: reportData,
            current_date: moment().format('DD MMMM YYYY, h:mm A'),
            NAMASTE_LOGO: findGeneralSetting.brand_logo_link,
            GENERATED_BY_USER: findUserDetail.user_full_name,
            CONFIDENTIALITY_STATEMENT: EMAIL_ADDRESS.CONFIDENTIALITY_STATEMENT,
            MICROFFICE_LOGO_URL: global.config.API_UPLOAD_URL + "email_logo/logo.png"
          };

          const htmlToSend = compiledTemplate(dataObj);

          const pdfBuffer: any = await generateFile(download, htmlToSend)

          res.set({
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename=generated.pdf',
            'Content-Length': pdfBuffer.length
          });

          console.log("PDF generated and sent successfully!");
          res.send(pdfBuffer);
        } catch (e) {
          console.log("error", e);
        }
      }
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: reportData,
      });
    }

  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getLeaveConsumptionReportList = async (req: any, res: any) => {
  try {
    const { branch_id, department_id, role_id, search, page, size, leave_type_id, sort, download, leave_year, start_date, end_date, report_mode } = req.query;

    const { limit, offset } = getPagination(Number(page), Number(size));
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id', [
        sequelize.fn(
          "concat",
          sequelize.col("user_first_name"),
          " ",
          sequelize.col("user_last_name"),
        ),
        "user_full_name",
      ]],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserIds = await getSuperAdminUserIdEnhanced(req.user.organization_id);
    const findUserRole: any = await Role.findOne({
      where: { id: getUserDetail.web_user_active_role_id }, raw: true
    });
    const whereObj: any = {
      user_status: { [Op.not]: [user_status.CANCELLED, user_status.DELETED] },
      organization_id: req.user.organization_id,
      id: {
        [Op.notIn]: getUserIds
      }
    };

    if (branch_id) whereObj.branch_id = branch_id;
    if (department_id) whereObj.department_id = department_id;
    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        Sequelize.where(
          Sequelize.col("user_email"),
          {
            [Op.like]: `%${search}%`,
          },
        )
      ];
    }
    const leavePeriodTypeCondition = report_mode == 'day' ? `AND ur.leave_period_type = 'day'` : `AND ur.leave_period_type = 'hour'`;

    const leaveYearCondition =
      start_date && end_date
        ? `AND ur.start_date <= '${end_date}' AND ur.end_date >= '${start_date}'`
        : start_date
          ? `AND ur.end_date >= '${start_date}'`
          : end_date
            ? `AND ur.start_date <= '${end_date}'`
            : "";

    const leaveTypeCondition = leave_type_id ? `AND ur.leave_request_type IN(${leave_type_id}) ` : "";
    const orderBy = [];

    if (sort) {
      const [key, direction] = sort.split(":"); // Expecting format like "user_full_name:ASC" or "total_used_leave_days:DESC"

      // Validate sorting direction
      const validDirections = ["ASC", "DESC"];
      const orderDirection = validDirections.includes(direction.toUpperCase()) ? direction.toUpperCase() : "ASC";

      // Add order condition dynamically
      if (key === "total_used_leave_days") {
        orderBy.push([sequelize.literal("total_used_leave_days"), orderDirection]);
        orderBy.push(["user_full_name", "ASC"]);
      } else if (key === "user_full_name") {
        orderBy.push(["user_full_name", orderDirection]);
      } else if (key === "employment_number") {
        orderBy.push(["employment_number", orderDirection]);
        orderBy.push(["user_full_name", "ASC"]);
      } else {
        orderBy.push(["user_full_name", "ASC"]); // Default ordering
      }
    } else {
      orderBy.push([sequelize.literal("total_used_leave_days"), 'DESC']); // Default ordering
      orderBy.push(["user_full_name", "ASC"]);
    }

    if (
      findUserRole &&
      (findUserRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
        findUserRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER || findUserRole.role_name == ROLE_CONSTANT.SIGNATURE) &&
      getUserDetail.branch_id
    ) {
      whereObj.branch_id = getUserDetail.branch_id;

      // Handle role hierarchy for both MORole and old role systems
      if (getUserDetail.user_role_id) {
        // Use MORole system - construct recursive query for mo_roles
        const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM mo_roles
            WHERE id = :activeRoleId AND organization_id = :organizationId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM mo_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
            WHERE r.organization_id = :organizationId
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId`;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
          replacements: {
            activeRoleId: getUserDetail.user_role_id,
            organizationId: getUserDetail.organization_id
          },
          type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles (MORole system)
        let whereStr = '';
        getChildRoles.forEach((child_role: any, index: number) => {
          if (index > 0) {
            whereStr += ' OR ';
          }
          whereStr += `User.user_role_id = ${child_role.id}`;
        });

        if (whereStr) {
          // Initialize Op.and array if not present
          if (!whereObj[Op.and]) {
            whereObj[Op.and] = [];
          }
          // Add the generated SQL condition
          whereStr += ` OR User.id=${req.user.id}`;
          whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
        }
      } else {
        // Fallback to old role system
        const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM nv_roles
            WHERE id = :activeRoleId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM nv_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId`;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
          replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
          type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles (old system)
        let whereStr = '';
        getChildRoles.forEach((child_role: any, index: number) => {
          if (index > 0) {
            whereStr += ' OR ';
          }
          whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = User.id)) > 0`;
        });

        if (whereStr) {
          // Initialize Op.and array if not present
          if (!whereObj[Op.and]) {
            whereObj[Op.and] = [];
          }
          // Add the generated SQL condition
          whereStr += ` OR User.id=${req.user.id}`;
          whereObj[Op.and].push(sequelize.literal(`(${whereStr})`));
        }
      }
    }
    const getUserListQuery: any = {
      where: whereObj,
      attributes: [
        "id",
        [
          sequelize.fn("concat", Sequelize.col("user_first_name"), " ", Sequelize.col("user_last_name")),
          "user_full_name"
        ],
        "user_email",
        "user_avatar",
        [
          sequelize.literal(
            `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
          ),
          "user_avatar_link",
        ],
        "user_avatar",
        [
          sequelize.literal(
            `ROUND((SELECT COALESCE(SUM(ur.leave_days), 0) FROM nv_request AS ur WHERE ur.from_user_id = User.id AND ur.request_status != '${request_status.REJECTED}' AND ur.request_status != '${request_status.CANCELLED}' ${leaveTypeCondition} ${leaveYearCondition} ${leavePeriodTypeCondition}), 2)`
          ),
          "total_used_leave_days"
        ],
        "employment_number",
        "user_status"
      ],
      include: [
        { model: Branch, as: "branch", attributes: ["id", "branch_name", "branch_color", "text_color"], required: false },
        { model: Department, as: "department", attributes: ["id", "department_name"], required: false }
      ],
      order: orderBy,
      raw: true,
      nest: true
    };

    if (page && size) {
      getUserListQuery.limit = limit;
      getUserListQuery.offset = offset;
    }

    if (role_id) {
      whereObj[Op.and] = [
        sequelize.literal(`(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id = ${role_id} AND role_id NOT IN (1))) `)
      ]
    } else {
      whereObj[Op.and] = [
        sequelize.literal(`(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1))) `)
      ]
    }

    const getUserList: any = await User.findAll(getUserListQuery);
    if (getUserList.length > 0) {
      for (const user of getUserList) {
        const leavePolicyData = (await getUserLeaveBalanceFunc(user.id, leave_year, null, leave_type_id, start_date, end_date, true, report_mode, req.user.organization_id)).data
        const totals = leavePolicyData.length > 0 ? leavePolicyData.reduce((acc: any, item: any) => {
          acc.leave_balance += formatNumber(item.leave_balance) || 0;
          acc.user_remaining_leave += formatNumber(item.user_remaining_leave) || 0;
          acc.planned_leaves += formatNumber(item?.used_leave.planned_leaves) || 0;
          acc.used_leaves += formatNumber(item.used_leave?.used_leaves) || 0;
          acc.total_user_leave += formatNumber(item.used_leave?.total) || 0;
          return acc;
        }, {
          leave_balance: 0,
          user_remaining_leave: 0,
          planned_leaves: 0,
          used_leaves: 0,
          total_user_leave: 0
        }) : {}
        user.leave_details = leavePolicyData
        user.leave_details_total = totals
        user.available_balance = totals.user_remaining_leave
        user.leave_balance = totals.user_remaining_leave
        // user.total_used_leave_days = user.total_user_leave
        // user.total_used_leave_days = totals.total_user_leave || 0
      }
    }
    delete getUserListQuery.attributes;
    const getUserCount = await User.count({ where: whereObj });
    const { total_pages } = getPaginatedItems(size, page, getUserCount || 0);

    if (download) {
      if (download == 'excel') {
        const workbook: any = await generateFile(download)
        const worksheet = workbook.addWorksheet('User cosumtion Data');

        // Define header priority
        const headerPriority = [
          'employment_number',
          'user_full_name',
          'user_email',
          'user_status',
          'branch',
          'department',
          'total_used_leave_days'
        ]; // Order of headers
        // Add title and metadata
        const titleRow = worksheet.addRow(['User leave consumption report']);

        // Get the total number of columns dynamically
        const totalColumns = headerPriority.length;
        const lastColumnLetter = String.fromCharCode(64 + totalColumns); // Convert number to Excel column letter

        // Merge title row across all columns
        worksheet.mergeCells(`A1:${lastColumnLetter}1`);

        // Style the title row
        titleRow.eachCell((cell: any) => {
          cell.font = { bold: true, size: 14 };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });

        if (branch_id) {
          const getBranchData: any = await Branch.findOne({ where: { id: branch_id, organization_id: req.user.organization_id }, raw: true });
          worksheet.addRow(['Branch:', getBranchData.branch_name]);
        }

        if (department_id) {
          const getDepartmentData: any = await Department.findOne({ where: { id: department_id, organization_id: req.user.organization_id }, raw: true });
          worksheet.addRow(['Department :', getDepartmentData.department_name]);
        }

        if (search) {
          worksheet.addRow(['Search:', search]);
        }

        if (role_id) {
          const getRoleData: any = await Role.findOne({ where: { id: role_id }, raw: true });
          worksheet.addRow(['Role:', getRoleData.role_name]);
        }

        if (leave_type_id) {
          const getLeaveData: any = await LeaveTypeModel.findOne({ where: { id: leave_type_id, organization_id: req.user.organization_id }, raw: true });
          worksheet.addRow(['Leave type:', getLeaveData.name]);
        }

        worksheet.addRow(['Report Date:', moment().format('DD MMMM YYYY, h:mm A')]);
        worksheet.addRow([]); // Empty row for spacing

        // Filter headers: Exclude unwanted keys and keep the ones in priority order
        const headers = headerPriority.filter(key => Object.keys(getUserList[0]).includes(key));


        // Add headers dynamically after metadata rows
        const headerRow = worksheet.addRow(headers.map(header => header.replace(/_/g, ' ').toUpperCase()));

        // Apply styling to the dynamically determined header row
        headerRow.eachCell((cell: any) => {
          cell.width = Math.max((cell.value || '').toString().length + 2, cell.width || 10);
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Dark blue background
          cell.font = { bold: true, color: { argb: 'FFFFFF' } }; // White text
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.border = {
            top: { style: 'thin', color: { argb: '000000' } },
            left: { style: 'thin', color: { argb: '000000' } },
            bottom: { style: 'thin', color: { argb: '000000' } },
            right: { style: 'thin', color: { argb: '000000' } },
          };
        });

        // Define column keys
        worksheet.columns = headers.map((header) => ({
          key: header,
          width: 25,
        }));
        // Add filtered data rows
        getUserList.forEach((row: any) => {
          const filteredRow = headers.reduce((obj: any, key) => {
            if (key == 'branch') {
              obj[key] = row[key].branch_name;
            } else if (key == 'department') {
              obj[key] = row[key].department_name;
            } else {
              obj[key] = row[key]; // Pick only required fields
            }
            return obj;
          }, {});
          worksheet.addRow(filteredRow);
        });

        worksheet.eachRow((row: any) => {
          row.eachCell((cell: any, colNumber: any) => {
            const column = worksheet.getColumn(colNumber);
            column.width = Math.max((cell.value || '').toString().length + 2, column.width || 10);
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.border = {
              top: { style: 'thin', color: { argb: '000000' } },
              left: { style: 'thin', color: { argb: '000000' } },
              bottom: { style: 'thin', color: { argb: '000000' } },
              right: { style: 'thin', color: { argb: '000000' } },
            };
          });
        });
        // Send the workbook as a buffer
        const buffer = await workbook.xlsx.writeBuffer();
        res.setHeader('Content-Disposition', 'attachment; filename=report.xlsx');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(buffer);
      } else if (download == 'csv') {

        // Transform data (handle nested objects)
        const transformedData = getUserList.map((row: any) => ({
          "EMPLOYMENT NUMBER": row.employment_number,
          "USER FULL NAME": row.user_full_name,
          "USER EMAIL": row.user_email,
          "USER STATUS": row.user_status,
          "BRANCH": row.branch?.branch_name || '',   // Extract branch name
          "DEPARTMENT": row.department?.department_name || '',  // Extract department name
          "TOTAL USED LEAVE DAYS": row.total_used_leave_days

        }));
        const buffer: any = await generateFile(download, transformedData)
        // Send the buffer in the response for file download
        res.setHeader('Content-Type', 'text/csv; charset=utf-8'); // Ensure the charset is specified
        res.setHeader('Content-Disposition', 'attachment; filename="report.csv"');
        res.setHeader('Content-Length', buffer.length.toString());
        res.end(buffer);
      }
      else if (download == 'pdf') {
        // Load HTML template
        const [contentHtml, footerHtml] = await Promise.all([
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.USER_LEAVE_CONSUMPTION_REPORT_PDF.template}.html`),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
          new Promise((resolve, reject) => {
            readHTMLFile(
              path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.FOOTER_TEMPLATE.template}.html`),
              (err: any, html: any) => {
                if (err) reject(err);
                else resolve(html);
              }
            );
          }),
        ]);

        const combinedTemplate = `${contentHtml}${footerHtml}`;
        try {
          const compiledTemplate = handlebars.compile(combinedTemplate);
          const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)

          const dataObj: any = {
            users: getUserList,
            current_date: moment().format('DD MMMM YYYY, h:mm A'),
            NAMASTE_LOGO: findGeneralSetting.brand_logo_link,
            GENERATED_BY_USER: getUserDetail.user_full_name,
            CONFIDENTIALITY_STATEMENT: EMAIL_ADDRESS.CONFIDENTIALITY_STATEMENT,
            MICROFFICE_LOGO_URL: global.config.API_UPLOAD_URL + "email_logo/logo.png"
          };
          if (branch_id) {
            const getBranchData: any = await Branch.findOne({ attributes: ['branch_name'], where: { id: branch_id, organization_id: req.user.organization_id }, raw: true });
            dataObj.branch = getBranchData.branch_name;
          }

          if (department_id) {
            const getDepartmentData: any = await Department.findOne({ attributes: ['department_name'], where: { id: department_id, organization_id: req.user.organization_id }, raw: true });
            dataObj.department = getDepartmentData.department_name;
          }

          if (role_id) {
            const getRoleData: any = await Role.findOne({ attributes: ['role_name'], where: { id: role_id }, raw: true });
            dataObj.role = getRoleData.role_name;
          }

          if (search) {
            dataObj.search = search
          }
          if (leave_type_id) {
            const getLeaveData: any = await LeaveTypeModel.findOne({ attributes: ['name'], where: { id: leave_type_id, organization_id: req.user.organization_id }, raw: true });
            dataObj.leave_type = getLeaveData.name
          }

          const htmlToSend = compiledTemplate(dataObj);

          const pdfBuffer: any = await generateFile(download, htmlToSend)

          res.set({
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename=generated.pdf',
            'Content-Length': pdfBuffer.length
          });

          console.log("PDF generated and sent successfully!");
          res.send(pdfBuffer);
        } catch (e) {
          console.log("error", e);
        }
      }
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        data: getUserList,
        message: res.__("SUCCESS_FETCHED"),
        count: getUserCount,
        page: parseInt(page),
        size: parseInt(size),
        total_pages
      });
    }


  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserLeaveBalance = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const { year, leave_period_type } = req.query
    const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: (await getUserLeaveBalanceFunc(id, year, leave_period_type, null, null, null, false, findGeneralSetting?.leave_period_type, req.user.organization_id)).data,
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Convert between hour-based and day-based leave formats
 * @param req
 * @param res
 * @returns
 */
const convertLeaveDaysFormat = async (req: any, res: any) => {
  try {
    const { leave_days_obj, conversion_type } = req.body;
    const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id);
    const workingHoursPerDay = findGeneralSetting?.working_hours_per_day || 8; // Default to 8 hours if not set

    if (!leave_days_obj || !conversion_type) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__('MISSING_REQUIRED_FIELDS')
      });
    }

    if (conversion_type !== 'day_to_hour' && conversion_type !== 'hour_to_day') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__('INVALID_CONVERSION_TYPE')
      });
    }

    let result: any = {};

    if (conversion_type === 'day_to_hour') {
      // Convert day-based leave to hour-based leave
      if (!leave_days_obj.Day || !Array.isArray(leave_days_obj.Day)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__('INVALID_DAY_FORMAT')
        });
      }

      const hourLeaves = [];

      for (const dayLeave of leave_days_obj.Day) {
        const { start_date, type } = dayLeave;

        if (!start_date || !type) {
          continue;
        }

        let hours = 0;

        // Calculate hours based on leave type
        if (type === 'full_day') {
          hours = workingHoursPerDay;
        } else if (type === 'first_half' || type === 'second_half') {
          hours = workingHoursPerDay / 2;
        }

        hourLeaves.push({
          date: start_date,
          total_hours: hours
        });
      }

      result = { Hour: hourLeaves };
    } else {
      // Convert hour-based leave to day-based leave
      if (!leave_days_obj.Hour || !Array.isArray(leave_days_obj.Hour)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__('INVALID_HOUR_FORMAT')
        });
      }

      const dayLeaves = [];

      for (const hourLeave of leave_days_obj.Hour) {
        const { date, total_hours } = hourLeave;

        if (!date || !total_hours) {
          continue;
        }

        let type = '';

        // Determine leave type based on hours
        if (total_hours >= workingHoursPerDay) {
          type = 'full_day';
        } else if (total_hours >= workingHoursPerDay / 2) {
          // If more than half day, consider it as first half
          type = 'first_half';
        } else {
          // If less than half day, consider it as second half
          type = 'second_half';
        }

        dayLeaves.push({
          start_date: date,
          type: type
        });
      }

      result = { Day: dayLeaves };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__('SUCCESS_CONVERSION'),
      data: result
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__('SOMETHING_WENT_WRONG'),
      data: error,
    });
  }
};


export default {
  leaveApply,
  getOwnLeaveList,
  getStaffLeaveList,
  approveRejectRequest,
  getUserLeaveListTimeWise,
  cancelLeaveRequest,
  LeaveRequestCount,
  getCalenderWiseLeaveList,
  getLeaveDetailById,
  getLeaveRuleList,
  modifyLeaveRule,
  getLeaveReportList,
  getLeaveTypeReport,
  getLeaveConsumptionReportList,
  getUserLeaveBalance,
  convertLeaveDaysFormat
};

