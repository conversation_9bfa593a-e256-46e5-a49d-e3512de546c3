import { DataTypes, Model, Optional } from "sequelize";
import { sequelize } from "./index";

export interface MOOrgWidgetAttributes {
  id: number;
  widget_id: number;
  organization_id: string;
  status: 'active' | 'deleted';
  created_by?: number;
  updated_by?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface MOOrgWidgetCreationAttributes extends Optional<MOOrgWidgetAttributes, 'id' | 'created_by' | 'updated_by' | 'createdAt' | 'updatedAt'> {}

export class MOOrgWidget extends Model<MOOrgWidgetAttributes, MOOrgWidgetCreationAttributes> implements MOOrgWidgetAttributes {
  public id!: number;
  public widget_id!: number;
  public organization_id!: string;
  public status!: 'active' | 'deleted';
  public created_by?: number;
  public updated_by?: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

MOOrgWidget.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    widget_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'mo_widgets',
        key: 'id'
      }
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('active', 'deleted'),
      allowNull: false,
      defaultValue: 'active'
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: "mo_org_widgets",
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['widget_id', 'organization_id']
      },
      {
        fields: ['organization_id']
      },
      {
        fields: ['widget_id']
      },
      {
        fields: ['status']
      }
    ]
  }
);

export { MOOrgWidget as default };
