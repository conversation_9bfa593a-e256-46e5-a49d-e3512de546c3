import { DataTypes, Model, Optional } from "sequelize";
import { sequelize } from "./index";

export interface MOOrgModuleAttributes {
  id: number;
  module_id: number;
  organization_id: string;
  status: 'active' | 'deleted';
  created_by?: number;
  updated_by?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface MOOrgModuleCreationAttributes extends Optional<MOOrgModuleAttributes, 'id' | 'created_by' | 'updated_by' | 'createdAt' | 'updatedAt'> {}

export class MOOrgModule extends Model<MOOrgModuleAttributes, MOOrgModuleCreationAttributes> implements MOOrgModuleAttributes {
  public id!: number;
  public module_id!: number;
  public organization_id!: string;
  public status!: 'active' | 'deleted';
  public created_by?: number;
  public updated_by?: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

MOOrgModule.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    module_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'mo_modules',
        key: 'id'
      }
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('active', 'deleted'),
      allowNull: false,
      defaultValue: 'active'
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: "mo_org_modules",
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['module_id', 'organization_id']
      },
      {
        fields: ['organization_id']
      },
      {
        fields: ['module_id']
      },
      {
        fields: ['status']
      }
    ]
  }
);

export { MOOrgModule as default };
