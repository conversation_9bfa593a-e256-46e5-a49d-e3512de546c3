import { Forecast, forecast_status } from "../models/Forecast";
import { StatusCodes } from 'http-status-codes';
import { Request, Response } from 'express';
import { forecast_budget_data_status, forecast_category_status, ForecastBugdetData, forecast_category_type } from "../models/ForecastBugdetData";
import { Branch, branch_status } from "../models/Branch";
import { payment_type_status, payment_type_usage, PaymentType } from "../models/PaymentType";
import { payment_type_category_status, PaymentTypeCategory } from "../models/PaymentTypeCategory";
import { col, fn, Op, Sequelize } from "sequelize";
import { sequelize } from "../models";
import { payment_type_category_branch_status, PaymentTypeCategoryBranch } from "../models/PaymentTypeCategoryBranch";
import { createNotification, generateForecastBudgetReport, getUserFullName, validateModulePermission, validateModulePartialPermission } from "../helper/common";
import { forecast_budget_data_history_status, ForecastBugdetDataHistory } from "../models/ForecastBugdetDataHistory";
import { NOTIFICATIONCONSTANT, ROLE_CONSTANT, NOTIFICATION_TYPE, REDIRECTION_TYPE, ROLE_PERMISSIONS } from "../helper/constant";
import { User, user_status } from "../models/User";
import moment from "moment";
import { forecast_history_status, ForecastHistory } from "../models/ForecastHistory";
import { Role } from "../models/Role";
import { Role as MORole } from "../models/MORole";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { UserBranch } from "../models/UserBranch";
import { generateFile } from "../helper/fileGeneration.service";
import { ForecastAssignBudget, user_assign_status } from "../models/ForecastAssignBudget";


const createForecast = async (req: Request, res: Response) => {
    try {
        const { forecast_year, branch_id, forecast_data, forecast_due_date, id }: any = req.body;
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'budget_forecast', // Budget & Forecast module slug
            ROLE_PERMISSIONS.CREATE
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER
        //     ]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;
        let getForecast: any = {}
        if (!hasPermission)
            return res.status(StatusCodes.FORBIDDEN).json({ status: false, message: res.__("PERMISSION_DENIED") });
        if (branch_id.length > 0) {
            for (const branch of branch_id) {
                const checkBranchExist = await Branch.findOne({ attributes: ['id'], where: { id: branch, branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id }, raw: true });
                if (!checkBranchExist) {
                    return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
                }
                if (!id) {
                    const checkForecastExist = await Forecast.findOne({ where: { forecast_year, branch_id: branch, forecast_status: { [Op.not]: forecast_status.DELETED } }, attributes: ['id'], raw: true });
                    if (checkForecastExist) {
                        return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FORECAST_ALREADY_EXISTS") });
                    }
                    getForecast = await Forecast.create({ forecast_year, branch_id: branch, forecast_status: forecast_status.ACTIVE, forecast_due_date: forecast_due_date, created_by: req.user.id, updated_by: req.user.id } as any);
                } else {
                    getForecast = await Forecast.findOne({ where: { id: id }, attributes: ['id'], raw: true });
                }
                if (getForecast && forecast_data.length > 0) {
                    const addForecastHistory = await ForecastHistory.create({ forecast_id: getForecast.id, forecast_history_status: forecast_history_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)
                    for (const forecast of forecast_data) {
                        if (forecast.forecast_category_data.length > 0) {
                            for (const forecast_category of forecast.forecast_category_data) {
                                const payment_type_category_ids = forecast_category?.payment_type_category_id.split(',');
                                const findPaymentTypeCategory = await PaymentTypeCategoryBranch.findAll({ where: { payment_type_category_id: { [Op.in]: payment_type_category_ids }, has_default_active: 1, branch_id: branch, payment_type_category_branch_status: payment_type_category_branch_status.ACTIVE, parent_id: { [Op.eq]: null } } as any, raw: true })
                                if (findPaymentTypeCategory.length > 0) {
                                    /** Get payment category id's */
                                    const paymentTypeCategoryIds = findPaymentTypeCategory.map(item => item.payment_type_category_id);
                                    /** Get it's status combined or separate */
                                    const isCombined = forecast_category.forecast_category_status === forecast_category_status.COMBINED;
                                    /** store it's category id based on it's status */
                                    const paymentIdsToStore = isCombined ? [paymentTypeCategoryIds.join(',')] : paymentTypeCategoryIds;

                                    for (const id of paymentIdsToStore) {
                                        const commonData = {
                                            forecast_id: getForecast?.id,
                                            payment_type_id: forecast_category?.payment_type_id,
                                            forecast_category_type: forecast?.forecast_category_type,
                                            bugdet_target_amount: 0.00,
                                            payment_type_category_id: id,
                                            forecast_category_status: isCombined
                                                ? forecast_category_status.COMBINED
                                                : forecast_category_status.SEPARATE, // Store status based on condition
                                            created_by: req.user.id,
                                            updated_by: req.user.id
                                        };

                                        await ForecastBugdetData.create({
                                            ...commonData,
                                            forecast_bugdet_data_status: forecast_budget_data_status.ACTIVE
                                        } as any);

                                        await ForecastBugdetDataHistory.create({
                                            ...commonData,
                                            forecast_history_id: addForecastHistory.id,
                                            forecast_bugdet_data_history_status: forecast_budget_data_history_status.ACTIVE
                                        } as any);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return res.status(StatusCodes.OK).json({ status: true, message: res.__("FORECAST_CREATED_SUCCESSFULLY"), data: getForecast });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__("FORECAST_ALREADY_EXISTS") });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

/** Assign create budget API or assign create budget access */
const assignBudget = async (req: Request, res: Response) => {
    try {
        const { forecast_id, user_id, is_create_budget, forecast_year, forecast_due_date, branch_id } = req.body

        // Add permission check for budget assignment
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'budget_forecast', // Budget & Forecast module slug
            ROLE_PERMISSIONS.CREATE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res.status(StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        let forecast_budget_year: any = ''
        let forecast_budget_id: any = ''
        let forecast_branch_id: any = ''
        /** check forecast exist then throw error*/
        const whereObj: any = { forecast_status: { [Op.not]: forecast_status.DELETED } }
        if (forecast_id) {
            whereObj.id = forecast_id
        } else {
            whereObj.forecast_year = forecast_year
            whereObj.branch_id = branch_id
        }
        const forecast: any = await Forecast.findOne({ where: whereObj, raw: true });
        if (forecast_id && !forecast) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("FORECAST_NOT_FOUND") });
        } else if (!forecast_id && forecast) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FORECAST_ALREADY_EXISTS") });
        }

        if (forecast_id) {

            /** check already assigned */
            const findAssignedUser: any = await ForecastAssignBudget.findOne({ where: { forecast_id: forecast_id, user_assign_status: user_assign_status.ACTIVE }, raw: true });
            if (findAssignedUser) {
                return res.status(StatusCodes.CONFLICT).json({ status: false, message: res.__("ERROR_BUDGET_ALREADY_ASSIGNED") });
            }
        }

        /**check if forecast is already created and then assigned to user */
        if (is_create_budget == false) {
            forecast_budget_year = forecast.forecast_year
            forecast_budget_id = forecast.id
            forecast_branch_id = forecast.branch_id
            await ForecastAssignBudget.create({ forecast_id, user_id, is_create_budget, created_by: req.user.id, updated_by: req.user.id } as any);
            await Forecast.update({ forecast_status: forecast_status.ASSIGNED }, { where: { id: forecast_id } } as any);
            // await ForecastHistory.update({ forecast_history_status: forecast_history_status.ASSIGNED }, { where: { forecast_id: forecast_id } } as any);

            /** Stored forecast assigned history */
            const forecastHistory = await ForecastHistory.create({ forecast_id: forecast_id, forecast_history_status: forecast_history_status.ASSIGNED, created_by: req.user.id, updated_by: req.user.id } as any)
            /** Get Budget Forecast data to store history */
            const getBudgetForecastData = await ForecastBugdetData.findAll({ where: { forecast_id: forecast_id }, raw: true })
            for (const data of getBudgetForecastData) {
                await ForecastBugdetDataHistory.create({
                    payment_type_category_id: data.payment_type_category_id,
                    forecast_history_id: forecastHistory.id,
                    forecast_id: forecast_id,
                    payment_type_id: data.payment_type_id,
                    forecast_category_type: data.forecast_category_type,
                    forecast_bugdet_data_history_status: forecast_budget_data_history_status.ACTIVE,
                    forecast_category_status: data.forecast_category_status,
                    january_amount: data.january_amount, february_amount: data.february_amount, march_amount: data.march_amount, april_amount: data.april_amount, may_amount: data.may_amount, june_amount: data.june_amount, july_amount: data.july_amount, august_amount: data.august_amount, september_amount: data.september_amount, october_amount: data.october_amount, november_amount: data.november_amount, december_amount: data.december_amount, bugdet_target_amount: data.bugdet_target_amount,
                    created_by: req.user.id,
                    updated_by: req.user.id
                } as any)
            }
        } else {
            /** create forecast  first and then assigned*/
            const getForecast = await Forecast.create({ forecast_year, forecast_due_date, branch_id, forecast_status: forecast_status.ASSIGNED, created_by: req.user.id, updated_by: req.user.id } as any);
            forecast_budget_year = getForecast.forecast_year
            forecast_budget_id = getForecast.id
            forecast_branch_id = getForecast.branch_id
            await ForecastAssignBudget.create({ forecast_id: getForecast.id, user_id, is_create_budget, created_by: req.user.id, updated_by: req.user.id } as any);
            await ForecastHistory.create({
                forecast_id: getForecast.id,
                forecast_history_status: forecast_history_status.ASSIGNED,
                created_by: req.user.id,
                updated_by: req.user.id
            } as any);
        }

        /** get user name */
        const getUserName: any = await User.findOne({
            where: { id: req.user.id }, attributes: [
                "id",
                [sequelize.fn("concat", sequelize.col("user_first_name"), " ", sequelize.col("user_last_name")),
                    "user_full_name",
                ]
            ], raw: true
        })

        /** send push notification to user whom assigned budget */
        const getRequestUserDetail: any = await User.findOne({
            attributes: ['id', 'appToken', 'webAppToken'],
            where: { id: user_id, organization_id: req.user.organization_id }, raw: true
        });
        if (!getRequestUserDetail) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("USER_NOT_FOUND") });
        }
        const getFullName: any = await getUserFullName(user_id)
        await createNotification([getRequestUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_ASSIGNED_BM.content(getUserName.user_full_name, forecast_budget_year), NOTIFICATIONCONSTANT.FORECAST_ASSIGNED_BM.heading, REDIRECTION_TYPE.FORECAST, forecast_budget_id, { forecast_id: forecast_budget_id, branch_id: forecast_branch_id })

        /** When budget is assigned then send push notification to all super admin except Director */
        const findAdminUsers = await User.findAll({
            attributes: ['id', 'appToken', 'webAppToken'],
            where: {
                user_status: {
                    [Op.not]: [
                        user_status.DELETED,
                        user_status.PENDING,
                        user_status.CANCELLED,
                    ],
                },
                user_role_id: {
                    [Op.in]: sequelize.literal(`
                        (SELECT mo_roles.id FROM mo_roles
                         INNER JOIN mo_permissions ON mo_roles.id = mo_permissions.role_id
                         INNER JOIN mo_modules ON mo_permissions.module_id = mo_modules.id
                         WHERE mo_modules.module = 'budget_forecast'
                         AND mo_permissions.permission >= ${ROLE_PERMISSIONS.EDIT}
                         AND mo_permissions.organization_id = '${req.user.organization_id}'
                         AND mo_permissions.status = 'active'
                         AND mo_roles.organization_id = '${req.user.organization_id}'
                         AND mo_roles.role_status = 'active')
                    `)
                },
                organization_id: req.user.organization_id
            }, raw: true, nest: true,
            group: ['id']
        }) || [];
        await createNotification(findAdminUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_ASSIGNED_ADMIN_NOTIFICATION.content(getUserName.user_full_name, forecast_budget_year, getFullName), NOTIFICATIONCONSTANT.FORECAST_ASSIGNED_ADMIN_NOTIFICATION.heading, REDIRECTION_TYPE.FORECAST, forecast_budget_id, { forecast_id: forecast_budget_id, branch_id: forecast_branch_id });
        return res.status(StatusCodes.OK).json({ status: true, message: res.__("SUCCESS_FORECAST_ASSIGNED") });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


const getForecastById = async (req: Request, res: Response) => {
    try {
        const { forecast_id } = req.params;

        // Fetch the main forecast
        const getForecastById: any = await Forecast.findOne({
            attributes: ['id', 'forecast_year', 'branch_id', 'forecast_status', 'forecast_due_date'],
            include: [{
                model: Branch,
                as: 'forecast_branch',
                attributes: ['id', 'branch_name'],
                where: { organization_id: req.user.organization_id }
            }],
            where: { id: forecast_id, forecast_status: { [Op.not]: forecast_status.DELETED } }
        });

        if (!getForecastById) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("FORECAST_NOT_FOUND"),
            });
        }

        // Fetch all ForecastBugdetData for the given forecast_id
        const forecastCategoryData = await ForecastBugdetData.findAll({
            attributes: [
                'forecast_category_type',
                [fn('GROUP_CONCAT', fn('DISTINCT', col('payment_type_category_id'))), 'payment_type_category_ids'],
                'payment_type_id',
                'forecast_bugdet_data_status',
                'forecast_category_status'
            ],
            where: {
                forecast_id: forecast_id,
                forecast_bugdet_data_status: { [Op.notIn]: [forecast_budget_data_status.INACTIVE] }
            },
            group: ['forecast_category_type', 'payment_type_id', 'forecast_bugdet_data_status', 'forecast_category_status'],
            raw: true
        });

        // Group by forecast_category_type
        const groupedForecastData = forecastCategoryData.reduce((acc: any, data: any) => {
            const category = acc.find((item: any) => item.forecast_category_type === data.forecast_category_type);
            const forecastItem = {
                payment_type_category_id: data.payment_type_category_ids,
                payment_type_id: data.payment_type_id,
                bugdet_target_amount: data.bugdet_target_amount,
                forecast_bugdet_data_status: data.forecast_bugdet_data_status,
                forecast_category_status: data.forecast_category_status
            };

            if (category) {
                category.forecast_category_data.push(forecastItem);
            } else {
                acc.push({
                    forecast_category_type: data.forecast_category_type,
                    forecast_category_data: [forecastItem]
                });
            }
            return acc;
        }, []);

        // Structure the final response
        const response = {
            id: getForecastById.id,
            forecast_year: getForecastById.forecast_year,
            forecast_due_date: getForecastById.forecast_due_date,
            branch: getForecastById.forecast_branch || {},
            forecast_data: groupedForecastData
        };

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: response
        });

    } catch (error) {
        console.error("Error Fetching Forecast:", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getForecastList = async (req: Request, res: Response) => {
    try {
        const { search, forecast_year, branch_id, page, size, view_type, payment_type_category_id, forecast_budget_status }: any = req.query
        const { limit, offset } = getPagination(Number(page), Number(size));
        const getUserDetail: any = await User.findOne({
            where: { id: req.user.id },
            attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'user_role_id'],
            raw: true
        });
        let budget_created = false
        if (!getUserDetail) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        }

        // Get current role using new MO roles system for web platform, fallback to old system
        let findCurrentRole: any = null;
        if (req.headers["platform-type"] == "web" && getUserDetail.user_role_id) {
            // Use new MORole system for web platform
            findCurrentRole = await MORole.findOne({
                where: {
                    id: getUserDetail.user_role_id,
                    organization_id: req.user.organization_id
                },
                attributes: ['id', 'role_name'],
                raw: true
            });
        }

        // Fallback to old Role system if new system doesn't return a role
        if (!findCurrentRole) {
            findCurrentRole = await Role.findOne({
                where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id },
                attributes: ['role_name'],
                raw: true
            });
        }
        const whereObj: any = { forecast_status: { [Op.not]: forecast_status.DELETED } }
        if (forecast_year) {
            whereObj.forecast_year = forecast_year;
        }
        if (branch_id) {
            whereObj.branch_id = { [Op.in]: branch_id.split(',').map(Number) }
        }

        /** if approved then get only approved data */
        if (forecast_budget_status == forecast_status.APPROVED) {
            whereObj.forecast_status = forecast_budget_status
        }

        /** if it's active then get all except approved and deleted */
        if (forecast_budget_status == forecast_status.ACTIVE) {
            whereObj.forecast_status = { [Op.not]: [forecast_status.DELETED, forecast_status.APPROVED] }
        }

        // Add branch_name filter with Op.like for pattern matching
        const branchWhere: any = {
            organization_id: req.user.organization_id
        };
        if (search) {
            branchWhere.branch_name = { [Op.like]: `%${search}%` };
        }

        /** check if user role is accountant then only show approved budget data */
        if (findCurrentRole?.role_name == ROLE_CONSTANT.ACCOUNTANT) {
            whereObj.forecast_status = forecast_status.APPROVED
        }

        if ((findCurrentRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER) || findCurrentRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER || findCurrentRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER || findCurrentRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER || findCurrentRole?.role_name == ROLE_CONSTANT.AREA_MANAGER) {
            const findUserBranch = await UserBranch.findAll({
                where: {
                    user_id: req.user.id
                }, attributes: ['branch_id'], raw: true
            })
            if (findCurrentRole?.role_name == ROLE_CONSTANT.AREA_MANAGER && findUserBranch.length > 0) {
                whereObj.branch_id = { [Op.in]: findUserBranch.map((branch: any) => branch.branch_id) }
            } else {
                if (getUserDetail.branch_id) {
                    whereObj.branch_id = getUserDetail.branch_id
                }
            }
        }
        const forecastListObj: any = {
            attributes: ['id', 'forecast_year', 'forecast_status', 'forecast_due_date'],
            include: [
                {
                    model: Branch,
                    as: 'forecast_branch',
                    attributes: ['id', 'branch_name', 'organization_id'],
                    where: branchWhere, // Apply branch_name filter
                },
                {
                    model: ForecastAssignBudget,
                    attributes: ['id', 'is_create_budget', 'user_id']
                },
            ],
            where: whereObj,
            distinct: true
        }

        if (view_type && view_type == 1) {
            forecastListObj.attributes = [
                'forecast_year',
                [sequelize.fn('GROUP_CONCAT', sequelize.literal('DISTINCT forecast_status')), 'forecast_status'],
                [sequelize.literal(`GROUP_CONCAT((SELECT branch_id FROM nv_branches AS forecast_branch WHERE id = Forecast.branch_id AND organization_id = '${req.user.organization_id}'))`), 'branch_id'],
                [sequelize.literal(`GROUP_CONCAT((SELECT branch_name FROM nv_branches AS forecast_branch WHERE id = Forecast.branch_id AND organization_id = '${req.user.organization_id}'))`), 'branch_name'],
            ];
            forecastListObj.include = []
            forecastListObj.group = ['forecast_year'];

        }

        if (page && size) {
            forecastListObj.limit = Number(limit);
            forecastListObj.offset = Number(offset);
        }

        // Check if user has partial permissions (can only see assigned budgets)
        const hasPartialPermission = await validateModulePartialPermission(
            req.user,
            req.user.organization_id,
            'budget_forecast',
            req?.headers?.["platform-type"]
        );

        console.log(`🔍 User ${req.user.id} has partial permission: ${hasPartialPermission}`);

        // Handle partial permissions - user can only see assigned budgets
        if (hasPartialPermission) {
            console.log(`🔒 User has partial permission - filtering to assigned budgets only`);

            // Get forecasts assigned to this user
            const assignedForecasts = await ForecastAssignBudget.findAll({
                where: {
                    user_id: req.user.id,
                    user_assign_status: user_assign_status.ACTIVE
                },
                attributes: ['forecast_id'],
                raw: true
            });

            const assignedForecastIds = assignedForecasts.map(af => af.forecast_id);

            if (assignedForecastIds.length > 0) {
                whereObj.id = { [Op.in]: assignedForecastIds };
                forecastListObj.where = whereObj;
                console.log(`🎯 User can see ${assignedForecastIds.length} assigned forecasts: ${assignedForecastIds.join(', ')}`);
            } else {
                // User has no assigned forecasts, return empty result
                console.log(`❌ User has no assigned forecasts`);
                whereObj.id = { [Op.in]: [-1] }; // This will return no results
                forecastListObj.where = whereObj;
            }
        }

        const { rows: getForecastListResponse, count }: any = await Forecast.findAndCountAll(forecastListObj)
        if (getForecastListResponse.length > 0) {
            for (let i = 0; i < getForecastListResponse.length; i++) {
                const forecastData = getForecastListResponse[i].get({ plain: true });
                if (view_type && view_type == 1) {
                    forecastData.id = null;
                    forecastData.forecast_branch = {}
                    forecastData.forecast_branch.id = forecastData.branch_id;
                    forecastData.forecast_branch.branch_name = forecastData.branch_name
                }
                if (view_type && view_type == 0) {
                    const ForecastAssignBudgets = forecastData.ForecastAssignBudgets[0]
                    forecastData.ForecastAssignBudgets = ForecastAssignBudgets
                }

                const forecastCategoryData = await ForecastBugdetData.findAll({
                    attributes: ['id', 'forecast_id', 'payment_type_id', 'bugdet_target_amount', 'forecast_bugdet_data_status'],
                    where: { forecast_id: forecastData.id, forecast_bugdet_data_status: { [Op.notIn]: [forecast_budget_data_status.INACTIVE] } }, raw: true
                });
                if (forecastCategoryData.length > 0) {
                    budget_created = true
                    // Check if any forecast_category_data has active status
                    const hasActiveStatus = forecastCategoryData.some((item: any) =>
                        item.forecast_bugdet_data_status == forecast_budget_data_status.ACTIVE
                    );
                    forecastData.has_active_status = hasActiveStatus
                } else {
                    forecastData.has_active_status = false
                }
                forecastData.forecast_bugdet_data = await generateForecastBudgetReport(forecastData.id ? forecastData?.id : null, findCurrentRole?.role_name, null, null, null, null, forecastData.forecast_year, view_type && view_type == 1 ? forecastData.branch_id : null, payment_type_category_id, view_type, null, null, req.user.organization_id);
                /** if budget is created then set key true */
                forecastData.budget_created = budget_created
                getForecastListResponse[i] = forecastData; // Replace with updated object

                delete getForecastListResponse[i].branch_id;
                delete getForecastListResponse[i].branch_name;
            }
        }

        let countAss = count
        if (view_type && view_type == 1) countAss = count.length;
        const { total_pages } = getPaginatedItems(
            size,
            page,
            countAss || 0,
        )
        // Respond with the data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getForecastListResponse,
            count: countAss,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });

    } catch (error) {
        console.log("error", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const getForecastCategoryByBranch = async (req: Request, res: Response) => {
    try {
        const { branch_id }: any = req.query;
        const isSingleBranch = branch_id?.split(',').length == 1;
        const fetchCategories = async (usage: string) => {
            const paymentTypeCategoryAttribute: any = [['id', 'payment_type_category_id'], 'payment_type_id', 'payment_type_category_title']
            if (isSingleBranch) {
                paymentTypeCategoryAttribute.push([sequelize.literal(`(SELECT payment_type_category_branch_status 
                      FROM nv_payment_type_category_branch 
                        WHERE branch_id = ${branch_id} 
                        AND parent_id is null 
                        AND payment_type_category_id = payment_type_category.id
                        )`), 'payment_type_category_status'])
            }
            const paymentTypeData = await PaymentType.findAll({
                attributes: ['id', 'payment_type_title', 'has_weekly_use', 'has_include_amount', 'has_field_currency', 'payment_type_usage', 'payment_type_status'],
                where: { payment_type_status: { [Op.not]: payment_type_status.DELETED }, payment_type_usage: usage, organization_id: req.user.organization_id },
                include: [{
                    model: PaymentTypeCategory,
                    as: "payment_type_category",
                    attributes: paymentTypeCategoryAttribute,
                    where: {
                        payment_type_category_status: { [Op.not]: payment_type_category_status.DELETED }, id: [sequelize.literal(
                            `(select payment_type_category_id from nv_payment_type_category_branch where branch_id In (${branch_id}) AND payment_type_category_branch_status='${payment_type_category_branch_status.ACTIVE}' AND has_default_active = 1 )`
                        )]
                    },
                }],
            });
            return paymentTypeData
        };

        const [getIncomeCategory, getOtherCategory, getExpenseCategory] = await Promise.all([
            fetchCategories(payment_type_usage.COLLECTION),
            fetchCategories(payment_type_usage.OTHER),
            fetchCategories(payment_type_usage.EXPENSE)
        ]);

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            category_list: {
                income: getIncomeCategory,
                other: getOtherCategory,
                expense: getExpenseCategory
            }
        });

    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const updateForecast = async (req: Request, res: Response) => {
    try {
        const { forecast_data, forecast_due_date }: any = req.body;
        const { forecast_id } = req.params;

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'budget_forecast', // Budget & Forecast module slug
            ROLE_PERMISSIONS.EDIT
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER
        //     ]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission)
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });

        const checkForecastExist = await Forecast.findOne({ where: { id: forecast_id }, raw: true });
        if (!checkForecastExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FORECAST_NOT_FOUND") });
        }

        const updatePbj: any = { updated_by: req.user.id };
        if (forecast_due_date) {
            updatePbj.forecast_due_date = forecast_due_date;
        }
        await Forecast.update(updatePbj, { where: { id: forecast_id } });

        const addForecastHistory = await ForecastHistory.create({
            forecast_id: forecast_id,
            forecast_history_status: forecast_history_status.ACTIVE,
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);

        const existingCategories: any = await ForecastBugdetData.findAll({
            where: { forecast_id: checkForecastExist.id, },
            attributes: ['payment_type_id', 'forecast_category_type', 'payment_type_category_id'],
            raw: true
        });
        /* check existing categories type wise */
        const existingCategoryTypes = existingCategories.map((item: any) => item.forecast_category_type);
        /* check incoming category types */
        const incomingCategoryTypes = forecast_data.map((item: any) => item.forecast_category_type);
        /* check removed category types */
        const removedCategoryTypes = existingCategoryTypes.filter((type: any) => !incomingCategoryTypes.includes(type));


        // Extract existing payment_type_id values for this forecast
        const existingPaymentTypeIds = existingCategories.map((item: any) => item.payment_type_id);

        // Extract incoming payment_type_id values from the new payload
        const incomingPaymentTypeIds = forecast_data.flatMap((category: any) =>
            category.forecast_category_data.map((data: any) => data.payment_type_id)
        );

        // Identify removed payment_type_id values
        const removedPaymentTypeIds = existingPaymentTypeIds.filter((id: any) => !incomingPaymentTypeIds.includes(id));
        const whereObject: any = {
            forecast_id: checkForecastExist.id
        }
        if (removedCategoryTypes.length > 0) {
            whereObject.forecast_category_type = { [Op.in]: removedCategoryTypes }
        }
        if (removedPaymentTypeIds.length > 0) {
            whereObject.payment_type_id = { [Op.in]: removedPaymentTypeIds }
        }
        await ForecastBugdetData.update(
            { forecast_bugdet_data_status: forecast_budget_data_status.INACTIVE },
            { where: whereObject }
        );

        if (forecast_data.length > 0) {
            for (const forecast of forecast_data) {
                if (forecast.forecast_category_data.length > 0) {
                    for (const forecast_category of forecast.forecast_category_data) {
                        const payment_type_category_ids = forecast_category?.payment_type_category_id.split(',');
                        const findPaymentTypeCateogry = await PaymentTypeCategoryBranch.findAll({
                            where: {
                                payment_type_category_id: { [Op.in]: payment_type_category_ids },
                                branch_id: checkForecastExist.branch_id,
                                parent_id: { [Op.is]: null }
                            }
                        } as any);

                        if (findPaymentTypeCateogry.length > 0) {
                            const paymentTypeCategoryIds = findPaymentTypeCateogry.map(item => item.payment_type_category_id);
                            forecast_category.payment_type_category_id = paymentTypeCategoryIds.join(',');
                            const isCombined = forecast_category.forecast_category_status === forecast_category_status.COMBINED;
                            const paymentIdsToStore: any = isCombined ? [paymentTypeCategoryIds.join(',')] : paymentTypeCategoryIds;

                            // **Step 1: Fetch existing stored categories for this forecast and payment type**
                            const existingCategories: any = await ForecastBugdetData.findAll({
                                where: {
                                    forecast_id: checkForecastExist.id,
                                    payment_type_id: forecast_category.payment_type_id,
                                    forecast_category_status: forecast_category_status.SEPARATE,
                                },
                                attributes: ['payment_type_category_id'],
                                raw: true
                            });

                            // Extract existing IDs
                            const existingCategoryIds = existingCategories.map((data: any) => data.payment_type_category_id);
                            // **Step 2: Find removed categories**
                            const removedCategories = existingCategoryIds.filter((id: any) =>
                                !paymentIdsToStore.map(String).includes(String(id))
                            );
                            if (removedCategories.length > 0) {
                                // **Step 3: Mark removed categories as inactive**
                                await ForecastBugdetData.update(
                                    { forecast_bugdet_data_status: forecast_budget_data_status.INACTIVE }, // Assuming there is a `status` column
                                    {
                                        where: {
                                            payment_type_category_id: { [Op.in]: removedCategories },
                                            forecast_id: checkForecastExist.id,
                                            payment_type_id: forecast_category.payment_type_id
                                        }
                                    }
                                );
                            }
                            for (const id of paymentIdsToStore) {
                                const commonData = {
                                    forecast_id: forecast_id,
                                    payment_type_id: forecast_category?.payment_type_id,
                                    forecast_category_type: forecast?.forecast_category_type,
                                    bugdet_target_amount: 0.00,
                                    payment_type_category_id: id,
                                    forecast_category_status: isCombined
                                        ? forecast_category_status.COMBINED
                                        : forecast_category_status.SEPARATE,
                                    created_by: req.user.id,
                                    updated_by: req.user.id
                                };
                                const whereForecastObject: any = {
                                    forecast_id: forecast_id,
                                    payment_type_id: forecast_category.payment_type_id,
                                }
                                if (forecast_category.forecast_category_status == forecast_category_status.SEPARATE) {
                                    whereForecastObject.payment_type_category_id = id
                                    whereForecastObject.forecast_category_status = forecast_category_status.SEPARATE
                                } else {
                                    whereForecastObject.forecast_category_status = forecast_category_status.COMBINED

                                }
                                const findForecastCategory: any = await ForecastBugdetData.findOne({
                                    attributes: ['id', 'payment_type_category_id', 'forecast_bugdet_data_status', 'forecast_category_status'],
                                    where: whereForecastObject,
                                    raw: true
                                });
                                if (findForecastCategory) {
                                    const updatedObject: any = {}
                                    const whereObject: any = {
                                        forecast_id: checkForecastExist.id,
                                        payment_type_id: forecast_category.payment_type_id
                                    }
                                    /** check if combined data exist with active status, then just update it's value */
                                    if (findForecastCategory.forecast_category_status == forecast_category_status.COMBINED && findForecastCategory.forecast_bugdet_data_status == forecast_budget_data_status.ACTIVE) {
                                        updatedObject.payment_type_category_id = id

                                        /** check if data exist with combined and update with separate, then update it;s payment_type_category_id value from combined to separate */
                                        if (forecast_category.forecast_category_status == forecast_category_status.SEPARATE) {
                                            updatedObject.forecast_category_status = forecast_category.forecast_category_status
                                        }
                                        whereObject.forecast_bugdet_data_status = forecast_budget_data_status.ACTIVE
                                    }

                                    /** check if it's already combined and it's status is inactive then just update it's payment_type_category_id and status from inactive to active */
                                    if (forecast_category.forecast_category_status == forecast_category_status.COMBINED && findForecastCategory.forecast_bugdet_data_status == forecast_budget_data_status.INACTIVE) {
                                        await ForecastBugdetData.update(
                                            { payment_type_category_id: id, forecast_bugdet_data_status: forecast_budget_data_status.ACTIVE }, // Assuming there is a `status` column
                                            {
                                                where: {
                                                    forecast_id: checkForecastExist.id,
                                                    payment_type_id: forecast_category.payment_type_id,
                                                    forecast_category_status: forecast_category_status.COMBINED
                                                }
                                            }
                                        );
                                        whereObject.forecast_category_status = forecast_category_status.SEPARATE
                                    }
                                    /** check if separate data is already exist and status is inactive then update it's status with active and inactive combined entry with same payment type id */
                                    if (findForecastCategory.forecast_bugdet_data_status == forecast_budget_data_status.INACTIVE && forecast_category.forecast_category_status == forecast_category_status.SEPARATE) {
                                        updatedObject.forecast_bugdet_data_status = forecast_budget_data_status.ACTIVE
                                        updatedObject.forecast_category_status = forecast_category.forecast_category_status
                                        whereObject.forecast_category_status = forecast_category_status.SEPARATE
                                        whereObject.payment_type_category_id = id

                                        /** Update when it's updated from combined to SEPARATE */
                                        await ForecastBugdetData.update(
                                            { forecast_bugdet_data_status: forecast_budget_data_status.INACTIVE }, // Assuming there is a `status` column
                                            {
                                                where: {
                                                    forecast_id: checkForecastExist.id,
                                                    payment_type_id: forecast_category.payment_type_id,
                                                    forecast_category_status: forecast_category_status.COMBINED
                                                }
                                            }
                                        );
                                    }
                                    await ForecastBugdetData.update(updatedObject, {
                                        where: whereObject
                                    });
                                } else {
                                    /** check if data coming from combined , then if it's already stored with separate data then update with inactive*/
                                    if (forecast_category.forecast_category_status == forecast_category_status.COMBINED) {
                                        await ForecastBugdetData.update(
                                            { forecast_bugdet_data_status: forecast_budget_data_status.INACTIVE }, // Assuming there is a `status` column
                                            {
                                                where: {
                                                    forecast_id: checkForecastExist.id,
                                                    payment_type_id: forecast_category.payment_type_id
                                                }
                                            }
                                        );
                                    }
                                    await ForecastBugdetData.create({
                                        ...commonData,
                                        forecast_bugdet_data_status: forecast_budget_data_status.ACTIVE
                                    } as any);
                                }

                                await ForecastBugdetDataHistory.create({
                                    ...commonData,
                                    forecast_history_id: addForecastHistory.id,
                                    forecast_bugdet_data_history_status: forecast_budget_data_history_status.ACTIVE
                                } as any);
                            }
                        }
                    }
                }
            }
            return res.status(StatusCodes.OK).json({ status: true, message: res.__("FORECAST_UPDATED_SUCCESSFULLY") });
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAILED_TO_UPDATE_FORECAST")
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const makeForecastLocked = async (req: Request, res: Response) => {
    try {
        const { forecast_id } = req.params
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'budget_forecast', // Budget & Forecast module slug
            ROLE_PERMISSIONS.EDIT
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.ACCOUNTANT
        //     ]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission)
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });

        const findForecast = await Forecast.findOne({ where: { id: forecast_id } })
        if (!findForecast) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FORECAST_NOT_FOUND")
            });
        }
        // const isForecastLocked = await Forecast.update( /*{ forecast_locked: forecast_locked } ,*/ { where: { id: findForecast.id } })
        // if (isForecastLocked.length > 0) {
        //     const findbmUsers = (await User.findAll({
        //         where: {
        //             user_status: {
        //                 [Op.not]: [
        //                     user_status.DELETED,
        //                     user_status.PENDING,
        //                     user_status.CANCELLED,
        //                 ],
        //             },
        //             id: {
        //                 [Op.in]: [
        //                     sequelize.literal(
        //                         `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN (SELECT id from nv_roles where role_name IN ('${ROLE_CONSTANT.BRANCH_MANAGER, ROLE_CONSTANT.HOTEL_MANAGER}')))`,
        //                     ),
        //                 ],
        //             },
        //             branch_id: findForecast.branch_id
        //         },
        //         group: ['id']
        //     })) || [];
        //     await createNotification(findbmUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_LOCKED.content(forecast_locked), NOTIFICATIONCONSTANT.FORECAST_LOCKED.heading, REDIRECTION_TYPE.FORECAST, findForecast.id, { forecast_id: findForecast.id, branch_id: findForecast.branch_id })
        //     if ((forecast_locked == true) || (forecast_locked == "true")) {
        //         return res.status(StatusCodes.OK).json({
        //             status: true,
        //             message: res.__("FORECAST_LOCKED_SUCCESSFULLY")
        //         });

        //     } else {
        //         return res.status(StatusCodes.OK).json({
        //             status: true,
        //             message: res.__("FORECAST_UNLOCKED_SUCCESSFULLY")
        //         });
        //     }
        // } else {
        //     if ((forecast_locked == true) || (forecast_locked == "true")) {
        //         return res.status(StatusCodes.BAD_REQUEST).json({
        //             status: false,
        //             message: res.__("FAILED_TO_LOCK_FORECAST")
        //         });
        //     } else {
        //         return res.status(StatusCodes.OK).json({
        //             status: true,
        //             message: res.__("FAILED_TO_UNLOCKED_FORECAST")
        //         });
        //     }
        // }
    } catch (error) {
        console.error("Error Fetching Forecast:", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

/**
 * Retrieves forecast budget details and generates chart data.
 * 
 * @param {Request} req - The request object containing parameters and query.
 * @param {Response} res - The response object for sending the result.
 */
const getForecastBudgetDetails = async (req: Request, res: Response) => {
    try {
        const { forecast_id } = req.params;
        const { format_type }: any = req.query;

        // Fetch the forecast details by ID and ensure it is active
        const getForecastBudgetDetails: any = await Forecast.findOne({
            where: { id: forecast_id, forecast_status: forecast_status.ACTIVE },
            attributes: ['id'],
            raw: true
        });

        // If no forecast details found, return a 404 error
        if (!getForecastBudgetDetails) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("FORECAST_NOT_FOUND") });
        }

        // Fetch user details to verify the requesting user
        const getUserDetail: any = await User.findOne({
            where: { id: req.user.id, organization_id: req.user.organization_id },
            attributes: ['id', 'web_user_active_role_id', 'user_role_id'],
            raw: true
        });
        if (!getUserDetail) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        }

        // Get current role using new MO roles system for web platform, fallback to old system
        let findUserRole: any = null;
        if (req.headers["platform-type"] == "web" && getUserDetail.user_role_id) {
            // Use new MORole system for web platform
            findUserRole = await MORole.findOne({
                where: {
                    id: getUserDetail.user_role_id,
                    organization_id: req.user.organization_id
                },
                attributes: ['id', 'role_name'],
                raw: true
            });
        }

        // Fallback to old Role system if new system doesn't return a role
        if (!findUserRole) {
            findUserRole = await Role.findOne({
                where: { id: getUserDetail.web_user_active_role_id },
                attributes: ['role_name'],
                raw: true
            });
        }

        // Generate report data for the forecast budget
        const getForecastBudgetReportData: any = await generateForecastBudgetReport(forecast_id, findUserRole.role_name, null, null, null, null, null, null, null, null, null, null, req.user.organization_id);

        const forecastBudgetChartData: any = [];
        const groupedColumns = getForecastBudgetReportData.columns_group;

        // Remove the first element if necessary
        groupedColumns.shift();

        // Iterate over grouped columns to generate chart data
        for (const element of groupedColumns) {
            const getForecastBudgetReportChartData = await generateForecastBudgetReport(forecast_id, findUserRole.role_name, format_type, element.forecast_category_type, element.payment_type_id, null, null, null, null, null, null, null, req.user.organization_id);
            forecastBudgetChartData.push({
                title: { text: element.content },
                chart_type: 'line',
                data: getForecastBudgetReportChartData?.data,
                // series: getForecastBudgetReportChartData?.series
            });

            // Special handling for INCOME category type to generate a meter chart
            if (element.forecast_category_type === forecast_category_type.INCOME) {
                let meterValue = '0.00';
                let meterMaxAmout = '0.00';

                // Calculate meter values from children elements
                for (const eChildren of element.children) {
                    meterMaxAmout = eChildren.is_budget ? getForecastBudgetReportData.target[eChildren.key] : meterMaxAmout;
                    meterValue = eChildren.is_actual ? getForecastBudgetReportData.total[eChildren.key] : meterValue;
                }

                forecastBudgetChartData.push({
                    chart_type: 'meter',
                    title: { text: 'Income Tracker: Predicted vs Current' },
                    value: Number(meterValue).toFixed(2),
                    scale: {
                        min: '0.00',
                        max: Number(meterMaxAmout).toFixed(2)
                    }
                });
            }
        }

        // Attach chart data to the report data
        getForecastBudgetReportData.forecastBudgetChartData = forecastBudgetChartData;

        // Respond with the successful data fetch
        return res.status(StatusCodes.OK).json({ status: true, message: res.__("SUCCESS_FETCHED"), data: getForecastBudgetReportData });

    } catch (error) {
        // Handle and log errors
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const downloadPdfExcel = async (req: Request, res: Response) => {
    try {
        const { forecast_id, file_type, forecast_year, branch_id, view_type }: any = req.query;

        // Fetch the forecast details by ID and ensure it is active
        const whereObj: any = { forecast_status: { [Op.not]: forecast_status.DELETED } }
        if (forecast_id) whereObj.id = forecast_id
        if (forecast_year) whereObj.forecast_year = forecast_year
        if (branch_id) whereObj.branch_id = { [Op.in]: branch_id.split(',').map((item: any) => Number(item)) }

        // Define the query object to fetch forecast list
        const forecastObj: any = {
            attributes: [
                'forecast_year',
                'forecast_status',
                [sequelize.literal(`GROUP_CONCAT((SELECT branch_id FROM nv_branches AS forecast_branch WHERE id = Forecast.branch_id AND organization_id = '${req.user.organization_id}'))`), 'branch_id'],
                [sequelize.literal(`GROUP_CONCAT((SELECT branch_name FROM nv_branches AS forecast_branch WHERE id = Forecast.branch_id AND organization_id = '${req.user.organization_id}'))`), 'branch_name']],
            where: whereObj,
            group: ['forecast_year', 'forecast_status'],
            raw: true
        }

        const getForecast: any = await Forecast.findOne(forecastObj)
        if (!getForecast) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FORECAST_NOT_FOUND")
            });
        }
        const dataset: any = await generateForecastBudgetReport(forecast_id ? forecast_id : null, null, null, null, null, null, forecast_year, view_type && view_type == 1 ? getForecast?.branch_id : null, null, view_type, null, null, req.user.organization_id);

        if (dataset) {
            if (file_type == 'excel') {
                const workbook: any = await generateFile(file_type)
                const worksheet: any = workbook.addWorksheet('Forecast Budget Report');

                // Add title and metadata
                const titleCell = worksheet.getCell('A1');
                titleCell.value = 'Forecast Budget Report';
                titleCell.font = { bold: true, size: 14 };
                titleCell.alignment = { horizontal: 'center' };

                const titleLength = titleCell.value.length;
                worksheet.getColumn('A').width = titleLength + 5;
                worksheet.mergeCells(`A1:${String.fromCharCode(65 + dataset.columnsArray.length - 1)}1`);

                // const findBranchName = await Branch.findOne({ where: { id: getForecast?.branch_id } })
                // const branchName = findBranchName && findBranchName.branch_name ? findBranchName.branch_name : "default"
                const branchName = getForecast && getForecast.branch_name ? getForecast.branch_name : '';
                worksheet.addRow(['Branch:', branchName]);
                worksheet.addRow(['Forecast year:', getForecast?.forecast_year]);
                worksheet.addRow(['Report Date:', moment().format('DD MMMM YYYY, h:mm A')]);
                worksheet.addRow([]); // Empty row for spacing

                // Create grouped header and subheader
                const headers: any = [];
                const columnsGroup = dataset.columns_group;

                // Prepare grouped and subheaders
                const groupedHeaderRow: any = [];
                const subHeaderRow: any = [];

                let groupIndex: any = 1;
                columnsGroup.forEach((group: any) => {
                    if (group.type === 'group') {
                        group.children.forEach((child: any) => {
                            groupedHeaderRow.push({ value: group.content, span: group.children.length, groupIndex: groupIndex });
                            headers.push(child.key)
                        });

                        // groupedHeaderRow.push({ value: group.content, span: group.children.length });
                        subHeaderRow.push(...group.children.map((child: any) => child.content));
                    } else {
                        groupedHeaderRow.push({ value: group.content, span: 1, groupIndex: groupIndex });
                        subHeaderRow.push("");
                        headers.push(group.key)
                    }
                    groupIndex++;
                });

                const mergedHeaders: any[] = []; // Array to store merged rows
                const currentColStartIndex = 1; // Initial column index

                // // Add grouped headers
                const groupedRow = worksheet.addRow(
                    groupedHeaderRow.map((header: any) => {
                        mergedHeaders.push(header.value); // Add the value to the mergedHeaders array
                        return header.value;
                    })
                );
                let currentCol: any = currentColStartIndex; // Start from the initial column index
                let groupIndexCheck = 0

                groupedRow.eachCell((cell: any, colNumber: number) => {
                    const header = groupedHeaderRow[colNumber - 1];
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.font = { bold: true };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Blue background
                    cell.font.color = { argb: 'FFFFFF' };

                    if (groupIndexCheck != header.groupIndex && header.span > 1) {
                        const startCol = getColumnLetter(currentCol);
                        const endCol = getColumnLetter(currentCol + header.span - 1);

                        worksheet.mergeCells(`${startCol}6:${endCol}6`);
                    }
                    currentCol++;
                    groupIndexCheck = header.groupIndex;
                });

                // // Add subheaders
                const subHeaderRowExcel = worksheet.addRow(subHeaderRow);

                subHeaderRowExcel.eachCell((cell: any, colNumber: number) => {
                    // Current row number
                    const currentRowNumber = cell.row;

                    // Get the cell above
                    const upperCell = worksheet.getRow(currentRowNumber - 1).getCell(colNumber);

                    // Merge the current cell with the upper cell
                    const startCell = `${cell.address}`; // Current cell address (e.g., B5)
                    const endCell = `${upperCell.address}`; // Upper cell address (e.g., B4)
                    if (!cell.value)
                        worksheet.mergeCells(`${endCell}:${startCell}`);

                    // Example: Logging the value of the cell above
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.font = { bold: true };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Dark gray background
                    cell.font.color = { argb: 'FFFFFF' }; // White font
                });

                // Adjust column widths
                subHeaderRowExcel.eachCell({ includeEmpty: true }, (cell: any, colNumber: any) => {
                    const column = worksheet.getColumn(colNumber);
                    column.width = Math.max((cell.value || '').toString().length + 2, column.width || 10);
                });
                const parseValue = (value: any) => {
                    return isNaN(value) ? value : value != 0 ? parseFloat(value) : 0; // Convert strings to numbers where applicable
                };

                // // Add data rows
                dataset.data.forEach((item: any) => {
                    // Add a blank row before mainTotal
                    if (item.type === 'mainTotal') {
                        worksheet.addRow([]); // Insert an empty row
                    }
                    const row = headers.map((_: any,) => {
                        const key = _;
                        if (item[key] === forecast_category_type.INCOME) {
                            item[key] = 'Income'
                        }
                        if (item[key] === forecast_category_type.OTHER) {
                            item[key] = 'Other'
                        }
                        if (item[key] === forecast_category_type.EXPENSE) {
                            item[key] = 'Expense'
                        }
                        return parseValue(item[key] !== undefined ? item[key] : '');
                    });
                    const dataRow = worksheet.addRow(row);
                    dataRow.eachCell({ includeEmpty: true }, (cell: any, colNumber: number) => {
                        const column = worksheet.getColumn(colNumber);
                        column.width = Math.max((cell.value || '').toString().length + 2, column.width || 10);

                        // Apply center alignment to all cells
                        cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    });
                    // Check if the row is a mainHeader
                    if (item.type === 'mainHeader') {
                        const lastCol = headers.length; // Get last column index
                        worksheet.mergeCells(dataRow.number, 2, dataRow.number, lastCol); // Merge row across all columns
                        dataRow.getCell(1).value = item.col1; // Set merged cell value
                        dataRow.getCell(2).value = ''; // Set empty value for merged columns
                        dataRow.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' }; // Center alignment

                        // Apply background color (EEF0FC) and white font to the entire row
                        dataRow.eachCell({ includeEmpty: true }, (cell: any) => {
                            cell.fill = {
                                type: 'pattern',
                                pattern: 'solid',
                                fgColor: { argb: 'EEF0FC' } // Light blue background
                            };
                            cell.font = { bold: true, color: { argb: '000000' } }; // black font
                            cell.alignment = { horizontal: 'center', vertical: 'middle' }; // Center alignment
                        });
                    }


                    if (item.payment_type_category_id === null) {
                        const lastCol = headers.length; // Get last column index
                        worksheet.mergeCells(dataRow.number, 2, dataRow.number, lastCol); // Merge row across all columns
                        dataRow.getCell(1).value = item.col1; // Set merged cell value
                        dataRow.getCell(2).value = ''; // Set empty value for merged columns
                        dataRow.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' }; // Center alignment

                        // Apply background color (EEF0FC) and white font to the entire row
                        dataRow.eachCell({ includeEmpty: true }, (cell: any) => {
                            cell.fill = {
                                type: 'pattern',
                                pattern: 'solid',
                                fgColor: { argb: 'ebebeb' } // Light blue background
                            };
                            cell.font = { bold: true, color: { argb: '000000' } }; // black font
                            cell.alignment = { horizontal: 'center', vertical: 'middle' }; // Center alignment
                        });
                    }
                    if (item.type === 'mainTotal') {
                        dataRow.getCell(1).value = item.col1; // Set merged cell value
                        dataRow.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' }; // Center alignment

                        // Apply background color (EEF0FC) and white font to the entire row
                        dataRow.eachCell({ includeEmpty: true }, (cell: any) => {
                            cell.fill = {
                                type: 'pattern',
                                pattern: 'solid',
                                fgColor: { argb: '0070C0' } // Blue background
                            };
                            cell.font = { bold: true, color: { argb: 'FFFFFF' } }; // black font
                            cell.alignment = { horizontal: 'center', vertical: 'middle' }; // Center alignment
                        });
                        worksheet.addRow([]); // Insert an empty row after mainTotal
                    }
                });

                // Add total row
                /**   const totalRowValues = headers.map((_: any) => {
                      console.log("headers", headers)
                      const key = _;
                      return parseValue(dataset.total[key] || '');
                  });
                  const totalRow = worksheet.addRow(totalRowValues);
                  totalRow.font = { bold: true };
                  totalRow.eachCell((cell: any, colNumber: number) => {
                      // Assign 0 if the cell value is null
                      if (cell.value === null) {
                          cell.value = 0;
                      }
                      const column = worksheet.getColumn(colNumber);
                      const desiredWidth = Math.max((cell.value || '').toString().length + 2, column.width || 10);
                      column.width = desiredWidth;
                      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'd9ead3' } }; // Light green background
                  });  
    
                // Add target row
                const totalTargetRowValues = headers.map((_: any) => {
                    const key = _;
                    return parseValue(dataset.target[key] || '');
                });
                const totalTarget = worksheet.addRow(totalTargetRowValues);
                totalTarget.font = { bold: true };
                totalTarget.eachCell((cell: any, colNumber: number) => {
                    // Assign 0 if the cell value is null
                    if (cell.value === null) {
                        cell.value = 0;
                    }
                    const column = worksheet.getColumn(colNumber);
                    const desiredWidth = Math.max((cell.value || '').toString().length + 2, column.width || 10);
                    column.width = desiredWidth;
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFCCCC' }, // Light pink background
                    };
                });  */

                // Apply border to all cells starting from row 4
                worksheet.eachRow({ includeEmpty: true }, (row: any, rowNumber: number) => {
                    if (rowNumber >= 6) { // Start applying borders from row 4
                        row.eachCell({ includeEmpty: true }, (cell: any, colNumber: number) => {
                            const column = worksheet.getColumn(colNumber);
                            const desiredWidth = Math.max((cell.value || '').toString().length + 2, column.width || 10);
                            column.width = desiredWidth;
                            // Apply a border to each cell
                            cell.border = {
                                top: { style: 'thin' },
                                left: { style: 'thin' },
                                bottom: { style: 'thin' },
                                right: { style: 'thin' },
                            };
                        });
                    }
                });
                // Function to dynamically adjust column widths based on content
                worksheet.columns.forEach((column: any/* , colNumber: number */) => {
                    column.eachCell({ includeEmpty: true }, (cell: any/* , rowNumber: number */) => {
                        const currentWidth = column.width || 10;  // Default minimum width
                        const valueLength = (cell.value || '').toString().length + 2;  // Add padding
                        column.width = Math.max(valueLength, currentWidth);
                    });
                });
                // Send the workbook as a buffer
                const buffer = await workbook.xlsx.writeBuffer();
                res.setHeader('Content-Disposition', 'attachment; filename=report.xlsx');
                res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                res.send(buffer);
            } else if (file_type == 'csv') {
                const { mappedData } = processDataset(dataset)
                const buffer: any = await generateFile(file_type, mappedData)
                // Send the buffer in the response for file download
                res.setHeader('Content-Type', 'text/csv; charset=utf-8'); // Ensure the charset is specified
                res.setHeader('Content-Disposition', 'attachment; filename="report.csv"');
                res.setHeader('Content-Length', buffer.length.toString());
                res.end(buffer);
            }
        }

    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const processDataset = (dataset: any) => {
    const data = dataset.data; // Row data

    /** commented below code as per new design 
     dataset.target = Object.keys(dataset.target).reduce((acc: any, key: any) => {
        acc[key] = dataset.target[key] ?? 0; // Nullish coalescing to set default to 0
        return acc;
    }, {}); */

    const flattenedData = [...data]; // Include totals at the end
    const columnsGroup = dataset.columns_group;
    // Prepare headers and mapped data
    const headers: string[] = [];
    const mappedData: any[] = [];

    // Flatten columns and construct headers
    columnsGroup.forEach((group: any) => {
        if (group.type === "group") {
            group.children.forEach((child: any) => {
                const columnLabel = `${group.content} - ${child.content}`;
                headers.push(columnLabel);
            });
        } else {
            const columnLabel = group.content;
            headers.push(columnLabel);
        }
    });

    // Map rows to the correct format based on column keys and groupings
    flattenedData.forEach((row: any) => {
        const newRow: any = {};

        columnsGroup.forEach((group: any) => {
            if (group.type === "group") {
                group.children.forEach((child: any) => {
                    const key = child.key;
                    let columnLabel = `${group.content} - ${child.content}`;
                    if (columnLabel in newRow) {
                        columnLabel = `${group.content} - ${child.content} `
                    }
                    newRow[columnLabel] = row[key] !== undefined ? row[key] : ''// Use your preferred default value;
                });
            } else if (group.type === "text" || group.type === "total") {
                const key = group.key;
                let columnLabel = group.content;
                if (columnLabel in newRow) {
                    columnLabel = `${group.content} `
                }
                if (group.type === "total") {
                    // Calculate total based on the provided `totalColumns`
                    const total = group.columns.reduce((sum: any, col: any) => {
                        const value = row[col] !== undefined ? row[col] : 0;
                        return sum + parseFloat(value);
                    }, 0).toFixed(2);
                    newRow[columnLabel] = total;
                } else {
                    if (row[key] === forecast_category_type.INCOME) {
                        row[key] = 'Income'
                    }
                    if (row[key] === forecast_category_type.OTHER) {
                        row[key] = 'Other'
                    }
                    if (row[key] === forecast_category_type.EXPENSE) {
                        row[key] = 'Expense'
                    }
                    newRow[columnLabel] = row[key] !== undefined ? row[key] : '' // Use your preferred default value;
                }
            }
        });

        mappedData.push(newRow);
    });

    return { headers, mappedData };
}

function getColumnLetter(colIndex: any) {
    let letter = '';
    while (colIndex > 0) {
        const mod = (colIndex - 1) % 26;
        letter = String.fromCharCode(65 + mod) + letter;
        colIndex = Math.floor((colIndex - 1) / 26);
    }
    return letter;
}

const saveForecastBudgetData = async (req: Request, res: Response) => {
    try {
        const { forecast_id, forecast_budget_data, forecast_budget_status } = req.body

        // Add permission check for budget/forecast editing
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'budget_forecast', // Budget & Forecast module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res.status(StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        // Check if user has partial permissions and validate assignment
        const hasPartialPermission = await validateModulePartialPermission(
            req.user,
            req.user.organization_id,
            'budget_forecast',
            req?.headers?.["platform-type"]
        );

        if (hasPartialPermission) {
            // User has partial permission - check if they are assigned to this forecast
            const assignedForecast = await ForecastAssignBudget.findOne({
                where: {
                    forecast_id: forecast_id,
                    user_id: req.user.id,
                    user_assign_status: user_assign_status.ACTIVE
                },
                raw: true
            });

            if (!assignedForecast) {
                console.log(`❌ User ${req.user.id} with partial permission tried to save data for unassigned forecast ${forecast_id}`);
                return res.status(StatusCodes.FORBIDDEN).json({
                    status: false,
                    message: res.__("PERMISSION_DENIED")
                });
            }

            console.log(`✅ User ${req.user.id} with partial permission is assigned to forecast ${forecast_id}`);
        }

        const checkForecastExist: any = await Forecast.findOne({
            where: { id: forecast_id }, raw: true
        });
        if (!checkForecastExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FORECAST_NOT_FOUND") });
        }
        const getUserDetail: any = await User.findOne({
            where: { id: req.user.id, organization_id: req.user.organization_id },
            attributes: ['web_user_active_role_id', 'user_active_role_id', 'id'],
            raw: true
        });
        if (!getUserDetail) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        }

        /** Get BM user for this particular forecast */
        const getAssignedUser: any = await ForecastAssignBudget.findOne({ where: { forecast_id: forecast_id, user_assign_status: user_assign_status.ACTIVE }, attributes: ['user_id'], raw: true })
        // if (!getAssignedUser) {
        //     return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        // }

        const anythingChanged: any = [false];
        let isStatusChanging = false
        for (const forecast_data of forecast_budget_data) {
            const whereObj: any = { forecast_id: forecast_id, /*forecast_category_type: forecast_data.forecast_category_type, */ forecast_bugdet_data_status: { [Op.not]: forecast_budget_data_status.INACTIVE } }
            // if (forecast_data.forecast_category_type != forecast_category_type.INCOME) {
            whereObj.payment_type_id = forecast_data.payment_type_id
            whereObj.payment_type_category_id = forecast_data.payment_type_category_id
            // }
            const findPaymentTypeBudget: any = await ForecastBugdetData.findOne({ where: whereObj, raw: true })
            if (findPaymentTypeBudget) {
                // Check if status is changing from "draft" to "submitted"
                const currentStatus = checkForecastExist.forecast_status;

                // Compare the current status with the new status
                if (currentStatus !== forecast_budget_status) {
                    // If they differ, set the flag to true
                    isStatusChanging = true
                }
                const changeStatus = await hasBudgetDataChanges(forecast_data, findPaymentTypeBudget)
                anythingChanged.push(isStatusChanging || changeStatus);
            }
        }
        if (anythingChanged.includes(true)) {
            await Forecast.update({ updated_by: req.user.id, forecast_status: forecast_budget_status == forecast_budget_data_status.DRAFT ? checkForecastExist.forecast_status : forecast_budget_status }, { where: { id: forecast_id } });
            const addForecastHistory = await ForecastHistory.create({ forecast_id: forecast_id, forecast_history_status: forecast_budget_status == forecast_budget_data_history_status.DRAFT ? forecast_status.ACTIVE : forecast_budget_status, created_by: req.user.id, updated_by: req.user.id } as any)
            if ((forecast_budget_data.length > 0) && addForecastHistory) {
                for (const forecast_data of forecast_budget_data) {
                    // Convert string to an array if it contains commas
                    const paymentTypeArray = forecast_data.payment_type_category_id.includes(',')
                        ? forecast_data.payment_type_category_id.split(',').map((id: string) => id.trim())
                        : [forecast_data.payment_type_category_id];

                    const whereObj: any = {
                        forecast_id: forecast_id,
                        forecast_bugdet_data_status: { [Op.not]: forecast_budget_data_status.INACTIVE },
                        payment_type_id: forecast_data.payment_type_id,
                        forecast_category_status: forecast_data.forecast_category_status,
                        [Op.or]: [
                            { payment_type_category_id: { [Op.in]: paymentTypeArray } },
                            ...paymentTypeArray.map((id: string) =>
                                Sequelize.literal(`FIND_IN_SET('${id}', payment_type_category_id) > 0`)
                            )
                        ]
                    };

                    const findPaymentTypeBudget: any = await ForecastBugdetData.findOne({ where: whereObj, raw: true })
                    if (findPaymentTypeBudget) {
                        const updateForecastData = await ForecastBugdetData.update({
                            january_amount: forecast_data.january_amount, february_amount: forecast_data.february_amount, march_amount: forecast_data.march_amount, april_amount: forecast_data.april_amount, may_amount: forecast_data.may_amount, june_amount: forecast_data.june_amount, july_amount: forecast_data.july_amount, august_amount: forecast_data.august_amount, september_amount: forecast_data.september_amount, october_amount: forecast_data.october_amount, november_amount: forecast_data.november_amount, december_amount: forecast_data.december_amount, bugdet_target_amount: forecast_data.bugdet_target_amount, forecast_bugdet_data_status: (forecast_budget_status === forecast_status.SUBMITTED || forecast_budget_status === forecast_status.APPROVED)
                                ? forecast_budget_data_status.ACTIVE
                                : forecast_budget_status, updated_by: req.user.id
                        }, { where: { id: findPaymentTypeBudget.id } } as any)
                        if (updateForecastData.length > 0) {
                            await ForecastBugdetDataHistory.create({
                                payment_type_category_id: forecast_data.payment_type_category_id,
                                forecast_history_id: addForecastHistory.id,
                                forecast_id: forecast_id,
                                payment_type_id: forecast_data.payment_type_id,
                                forecast_category_type: findPaymentTypeBudget.forecast_category_type,
                                forecast_bugdet_data_history_status: (forecast_budget_status === forecast_history_status.SUBMITTED || forecast_budget_status === forecast_history_status.APPROVED)
                                    ? forecast_budget_data_history_status.ACTIVE
                                    : forecast_budget_status,
                                forecast_category_status: forecast_data.forecast_category_status,
                                january_amount: forecast_data.january_amount, february_amount: forecast_data.february_amount, march_amount: forecast_data.march_amount, april_amount: forecast_data.april_amount, may_amount: forecast_data.may_amount, june_amount: forecast_data.june_amount, july_amount: forecast_data.july_amount, august_amount: forecast_data.august_amount, september_amount: forecast_data.september_amount, october_amount: forecast_data.october_amount, november_amount: forecast_data.november_amount, december_amount: forecast_data.december_amount, bugdet_target_amount: forecast_data.bugdet_target_amount,
                                created_by: req.user.id,
                                updated_by: req.user.id
                            } as any)
                        }
                    }
                }
                /** send push notification when it's send for approval */
                if (forecast_budget_status === forecast_status.SUBMITTED) {
                    /** check if current role is BM then send push to it's higher all users, except area manager  */
                    const getFullName: any = await getUserFullName(req.user.id)

                    /** get Branch name */
                    const findBranchName = (await Branch.findOne({ where: { id: checkForecastExist.branch_id, organization_id: req.user.organization_id } }))?.branch_name || "default";

                    /** Get users with budget approval permissions using new MORole system */
                    const findAdminUsers = await User.findAll({
                        attributes: ['id', 'appToken', 'webAppToken'],
                        where: {
                            user_status: {
                                [Op.not]: [
                                    user_status.DELETED,
                                    user_status.PENDING,
                                    user_status.CANCELLED,
                                ],
                            },
                            organization_id: req.user.organization_id,
                            user_role_id: {
                                [Op.in]: sequelize.literal(`
                                    (SELECT mo_roles.id FROM mo_roles
                                     INNER JOIN mo_permissions ON mo_roles.id = mo_permissions.role_id
                                     INNER JOIN mo_modules ON mo_permissions.module_id = mo_modules.id
                                     WHERE mo_modules.module = 'budget_forecast'
                                     AND mo_permissions.permission >= ${ROLE_PERMISSIONS.EDIT}
                                     AND mo_permissions.organization_id = '${req.user.organization_id}'
                                     AND mo_permissions.status = 'active'
                                     AND mo_roles.organization_id = '${req.user.organization_id}'
                                     AND mo_roles.role_status = 'active')
                                `)
                            }
                        },
                        raw: true,
                        nest: true,
                        group: ['id']
                    }) || [];
                    await createNotification(findAdminUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_DETAIL_UPDATED_BM.content(getFullName, findBranchName, checkForecastExist.forecast_year), NOTIFICATIONCONSTANT.FORECAST_DETAIL_UPDATED_BM.heading, REDIRECTION_TYPE.FORECAST, checkForecastExist.id, { forecast_id: checkForecastExist.id, branch_id: checkForecastExist.branch_id });
                }

                /** send push notification when it's final approved by director */
                if (forecast_budget_status === forecast_status.APPROVED) {
                    /** check if current role is BM then send push to it's higher all users, except area manager  */
                    const getFullName: any = await getUserFullName(req.user.id)
                    /** get Branch name */
                    const findBranchName = (await Branch.findOne({ where: { id: checkForecastExist.branch_id, organization_id: req.user.organization_id } }))?.branch_name || "default";

                    /** Get BM to higher level all users */
                    const findAdminUsers = await User.findAll({
                        attributes: ['id', 'appToken', 'webAppToken'],
                        where: {
                            user_status: {
                                [Op.not]: [
                                    user_status.DELETED,
                                    user_status.PENDING,
                                    user_status.CANCELLED,
                                ],
                            },
                            id: {
                                [Op.in]: [
                                    sequelize.literal(
                                        `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN (SELECT id from nv_roles where role_name IN ('${ROLE_CONSTANT.SUPER_ADMIN}', '${ROLE_CONSTANT.ADMIN}', '${ROLE_CONSTANT.ACCOUNTANT}','${ROLE_CONSTANT.DIRECTOR}','${ROLE_CONSTANT.HR}')))`,
                                    ),
                                ],
                            },
                            organization_id: req.user.organization_id
                        }, raw: true, nest: true,
                        group: ['id']
                    }) || [];
                    await createNotification(findAdminUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_APPROVED.content(findBranchName, getFullName, checkForecastExist.forecast_year), NOTIFICATIONCONSTANT.FORECAST_APPROVED.heading, REDIRECTION_TYPE.FORECAST, checkForecastExist.id, { forecast_id: checkForecastExist.id, branch_id: checkForecastExist.branch_id });


                    /** send push notification to user whom assigned budget */
                    if (getAssignedUser) {
                        const getRequestUserDetail: any = await User.findOne({
                            attributes: ['id', 'appToken', 'webAppToken'],
                            where: { id: getAssignedUser.user_id, organization_id: req.user.organization_id }, raw: true
                        });
                        if (!getRequestUserDetail) {
                            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
                        }
                        const getFullName: any = await getUserFullName(getAssignedUser.user_id)
                        await createNotification(findAdminUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.FORECAST_APPROVED.content(findBranchName, getFullName, checkForecastExist.forecast_year), NOTIFICATIONCONSTANT.FORECAST_APPROVED.heading, REDIRECTION_TYPE.FORECAST, checkForecastExist.id, { forecast_id: checkForecastExist.id, branch_id: checkForecastExist.branch_id });
                    }
                }
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("FORECAST_BUGDET_DATA_UPDATED_SUCCESSFULLY") // Updated i18n message key
                });
            } else {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                    status: false,
                    message: res.__("FAILED_TO_UPDATE_BUGDET_DATA") // Updated i18n message key
                });
            }
        } else {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FORECAST_BUGDET_DATA_UPDATED_SUCCESSFULLY") // Updated i18n message key
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

/**
 * Retrieves the list of forecast history for a given forecast ID with pagination.
 * 
 * @param {Request} req - The request object containing query parameters.
 * @param {Response} res - The response object for sending the result.
 */
const getForecastHistoryList = async (req: Request, res: Response) => {
    try {
        const { forecast_id, page, size }: any = req.query;

        // Get pagination details
        const { limit, offset } = getPagination(page, size);

        // Define the query object for fetching forecast history
        const qryObj: any = {
            attributes: ['id', 'forecast_id', 'forecast_history_status', 'updatedAt'],
            include: [
                {
                    model: User,
                    as: "forecast_history_created_by",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                    ],
                    where: { organization_id: req.user.organization_id }
                },
            ],
            where: { forecast_id: forecast_id, /*forecast_history_status: forecast_history_status.ACTIVE */ },
            order: [['id', 'DESC']],
        };

        // Apply pagination if page and size are provided
        if (page && size) {
            qryObj.limit = Number(limit);
            qryObj.offset = Number(offset);
        }

        // Fetch forecast history from the database
        const { count, rows: getForecastHistoryResponse }: any = await ForecastHistory.findAndCountAll(qryObj);
        /** get forecast data */
        const forecast: any = await Forecast.findOne({ where: { id: forecast_id }, attributes: ['id', 'forecast_year'], raw: true })

        // Calculate total pages for pagination
        const { total_pages } = getPaginatedItems(
            Number(size),
            Number(page),
            count || 0,
        );
        // Parse the response to JSON
        const getForecastHistoryList = JSON.parse(JSON.stringify(getForecastHistoryResponse)).map((item: any) => ({
            ...item,
            forecast_year: forecast?.forecast_year || null, // Assigning forecast_year to each record
        }));

        // Respond with the data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getForecastHistoryList,
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });

    } catch (error) {
        console.log("error", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

/**
 * Retrieves a specific forecast history by ID.
 * 
 * @param {Request} req - The request object containing the forecast history ID.
 * @param {Response} res - The response object for sending the result.
 * @returns {Promise<void>}
 */
const getForecastHistoryDetails = async (req: Request, res: Response) => {
    try {
        const { forecast_history_id }: any = req.params;
        const { forecast_budget_status } = req.query;
        let previous_history_id: any = null

        // Define the query object to fetch forecast history
        const qryObj: any = {
            attributes: ['id', 'forecast_id', 'forecast_history_status', 'updatedAt', 'createdAt'],
            include: [
                {
                    model: User,
                    as: "forecast_history_created_by",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                    ],
                    where: { organization_id: req.user.organization_id }
                },
            ],
            where: { id: forecast_history_id, /**forecast_history_status: forecast_history_status.ACTIVE **/ },
            raw: true, nest: true
        };

        // Fetch forecast history from the database
        const getForecastHistoryResponse: any = await ForecastHistory.findOne(qryObj);
        if (!getForecastHistoryResponse) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("ERROR_FORECAST_HISTORY_NOT_FOUND"),
            });
        }

        const forecastListObj: any = {
            attributes: ['id', 'forecast_year'],
            include: [
                {
                    model: Branch,
                    as: 'forecast_branch',
                    attributes: ['id', 'branch_name'],
                    where: { organization_id: req.user.organization_id }
                },
            ],
            where: { id: getForecastHistoryResponse.forecast_id }, raw: true, nest: true
        }
        const getForecastBranchData: any = await Forecast.findOne(forecastListObj);

        // Fetch the previous forecast history entry
        const previousForecastHistory = await ForecastHistory.findOne({
            attributes: ['id', 'forecast_id', 'forecast_history_status', 'updatedAt', 'createdAt'],
            where: {
                forecast_id: getForecastHistoryResponse.forecast_id,
                createdAt: { [Op.lt]: getForecastHistoryResponse.createdAt },
                forecast_history_status: { [Op.not]: forecast_history_status.DELETED },
            },
            order: [['createdAt', 'DESC']],
            limit: 1,
            raw: true,
            nest: true,
        });
        if (previousForecastHistory) {
            previous_history_id = previousForecastHistory.id
        }
        // Parse the response to JSON
        const getForecastHistoryList = JSON.parse(JSON.stringify(getForecastHistoryResponse));
        getForecastHistoryList.forecast_data = getForecastBranchData ? getForecastBranchData : null
        getForecastHistoryList.forecast_bugdet_data = null;
        if (getForecastHistoryList) {
            // Generate forecast budget report data for the history
            getForecastHistoryList.forecast_bugdet_data = await generateForecastBudgetReport(
                getForecastHistoryList.forecast_id,
                null,
                null,
                null,
                null,
                getForecastHistoryList.id,
                null,
                null,
                null,
                0,
                previous_history_id,
                forecast_budget_status,
                req.user.organization_id
            );
        }

        // Respond with the data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getForecastHistoryList,
        });

    } catch (error) {
        console.log("error", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


/**
 * Retrieves forecast list with chart data.
 * 
 * @param {Request} req - The request object containing parameters and query.
 * @param {Response} res - The response object for sending the result.
 * @returns {Promise<void>}
 */
const getForecastChartList = async (req: Request, res: Response) => {
    try {
        const { search, forecast_year, branch_id, page, size, view_type }: any = req.query
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Define the query object to fetch forecast list
        /** Get branches based on organization id */
        const whereObj: any = {
            forecast_status: forecast_status.ACTIVE, branch_id: {
                [Op.in]: sequelize.literal(`(
                SELECT id FROM nv_branches 
                WHERE organization_id = '${req.user.organization_id}'
            )`)
            }
        }
        if (forecast_year) {
            whereObj.forecast_year = forecast_year;
        }
        if (branch_id) {
            whereObj.branch_id = branch_id
        }

        // Fetch user details to verify the requesting user
        const getUserDetail: any = await User.findOne({
            where: { id: req.user.id },
            attributes: ['id', 'web_user_active_role_id', 'user_role_id'],
            raw: true
        });
        if (!getUserDetail) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        }

        // Get current role using new MO roles system for web platform, fallback to old system
        let findUserRole: any = null;
        if (req.headers["platform-type"] == "web" && getUserDetail.user_role_id) {
            // Use new MORole system for web platform
            findUserRole = await MORole.findOne({
                where: {
                    id: getUserDetail.user_role_id,
                    organization_id: req.user.organization_id
                },
                attributes: ['id', 'role_name'],
                raw: true
            });
        }

        // Fallback to old Role system if new system doesn't return a role
        if (!findUserRole) {
            findUserRole = await Role.findOne({
                where: { id: getUserDetail.web_user_active_role_id },
                attributes: ['role_name'],
                raw: true
            });
        }

        // Add branch_name filter with Op.like for pattern matching
        const branchWhere: any = {};
        if (search) {
            branchWhere.branch_name = { [Op.like]: `%${search}%` };
        }

        // Define the query object to fetch forecast list
        const forecastListObj: any = {
            attributes: ['id', 'forecast_year', 'forecast_status'],
            include: [
                {
                    model: Branch,
                    as: 'forecast_branch',
                    attributes: ['id', 'branch_name'],
                    where: branchWhere, // Apply branch_name filter
                },
            ],
            where: whereObj
        }

        if (view_type && view_type == 1) {
            forecastListObj.attributes = [
                'forecast_year',
                'forecast_status',
                [sequelize.fn('GROUP_CONCAT', sequelize.col('branch_id')), 'branch_id'],
                [sequelize.literal(`GROUP_CONCAT((SELECT branch_name FROM nv_branches AS forecast_branch WHERE id = Forecast.branch_id))`), 'branch_name'],
            ];
            forecastListObj.include = [];
            forecastListObj.group = ['forecast_year', 'forecast_status'];
        }

        if (page && size) {
            forecastListObj.limit = Number(limit);
            forecastListObj.offset = Number(offset);
        }

        // Fetch forecast list from the database
        const { rows: getForecastListResponse, count }: any = await Forecast.findAndCountAll(forecastListObj);
        const getForecastList = JSON.parse(JSON.stringify(getForecastListResponse));
        if (getForecastList.length > 0) {
            for (let i = 0; i < getForecastList.length; i++) {
                const forecastData = getForecastList[i];
                if (view_type && view_type == 1) {
                    getForecastList[i].id = null;
                    getForecastList[i].forecast_locked = null;
                    getForecastList[i].forecast_branch = {}
                    getForecastList[i].forecast_branch.id = null;
                    getForecastList[i].forecast_branch.branch_name = forecastData.branch_name
                }
                // Generate report data for the forecast budget
                const getForecastBudgetReportData: any = await generateForecastBudgetReport(forecastData.id ? forecastData.id : null, findUserRole.role_name, null, null, null, null, forecastData.forecast_year, view_type && view_type == 1 ? forecastData.branch_id : null, null, null, null, null, req.user.organization_id);

                // Initialize an empty array to store chart data
                const forecastBudgetChartData: any = [];
                // Remove the first element if necessary
                const groupedColumns = getForecastBudgetReportData.columns_group;
                groupedColumns.shift();
                // Iterate over grouped columns to generate chart data
                for (const element of groupedColumns) {
                    // Generate report data for the forecast budget with chart type
                    const getForecastBudgetReportChartData = await generateForecastBudgetReport(
                        forecastData.id,
                        findUserRole.role_name,
                        'chart',
                        element.forecast_category_type,
                        element.payment_type_id,
                        null,
                        forecastData.forecast_year,
                        view_type && view_type == 1 ? forecastData.branch_id : null,
                        null,
                        null,
                        null,
                        null,
                        req.user.organization_id
                    );
                    forecastBudgetChartData.push({
                        title: { text: element.content },
                        chart_type: 'line',
                        data: getForecastBudgetReportChartData?.data,
                        // series: getForecastBudgetReportChartData?.series,
                    });

                    // Add a meter chart for income category
                    if (element.forecast_category_type == forecast_category_type.INCOME) {

                        let meterValue = '0.00';
                        let meterMaxAmout = '0.00';
                        // Iterate over children to find the maximum and actual values
                        for (const eChildren of element.children) {
                            meterMaxAmout = eChildren.is_budget ? getForecastBudgetReportData.target[eChildren.key] : meterMaxAmout;
                            meterValue = eChildren.is_actual ? getForecastBudgetReportData.total[eChildren.key] : meterValue;
                        }

                        forecastBudgetChartData.push({
                            chart_type: 'meter',
                            title: { text: 'Income Tracker: Predicted vs Current' },
                            value: Number(meterValue).toFixed(2),
                            scale: {
                                min: '0.00',
                                max: Number(meterMaxAmout).toFixed(2)
                            }
                        })
                    }
                }

                getForecastList[i].forecastBudgetChartData = forecastBudgetChartData

                delete getForecastList[i].branch_id;
                delete getForecastList[i].branch_name;
            }
        }
        let countAss = count
        if (view_type && view_type == 1) countAss = count.length;
        const { total_pages } = getPaginatedItems(
            size,
            page,
            countAss || 0,
        )
        // Respond with the data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getForecastList,
            count: countAss,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });

    } catch (error) {
        console.log("error", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

// Utility function to check if update is needed
const hasBudgetDataChanges = async (newData: any, existingData: any) => {
    const fieldsToCheck = [
        'january_amount', 'february_amount', 'march_amount', 'april_amount', 'may_amount',
        'june_amount', 'july_amount', 'august_amount', 'september_amount', 'october_amount',
        'november_amount', 'december_amount', 'bugdet_target_amount'
    ];
    return fieldsToCheck.some(field => newData[field] != existingData[field]);
};

/** Delete Budget API */
const deleteBudget = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { forecast_budget_status } = req.body

        // Add permission check for budget deletion
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'budget_forecast', // Budget & Forecast module slug
            ROLE_PERMISSIONS.DELETE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res.status(StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        const checkForecastExist = await Forecast.findOne({ where: { id: id }, attributes: ['id', 'forecast_status'], raw: true });
        if (!checkForecastExist) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FORECAST_NOT_FOUND") });
        }
        let message: any = ''

        /** check if user revoked forecast from BM */
        if (forecast_budget_status == forecast_status.REVOKED) {
            /** check forecast status is approved  */
            if (checkForecastExist.forecast_status == forecast_status.APPROVED) {
                return res.status(StatusCodes.FORBIDDEN).json({ status: false, message: res.__("ERROR_FORECAST_ALREADY_APPROVED", { type: 'revoked' }) });
            }

            /** check forecast status is request for approval mode */
            if (checkForecastExist.forecast_status == forecast_status.SUBMITTED) {
                return res.status(StatusCodes.FORBIDDEN).json({ status: false, message: res.__("ERROR_FORECAST_UNDER_APPROVAL", { type: 'revoked' }) });
            }
            await Forecast.update({ forecast_status: forecast_status.REVOKED }, { where: { id: id } })
            await ForecastAssignBudget.update({ user_assign_status: user_assign_status.INACTIVE }, { where: { forecast_id: id } })
            // await ForecastBugdetData.update({ forecast_bugdet_data_status: forecast_budget_data_status.INACTIVE }, { where: { forecast_id: id } })
            await ForecastHistory.create({ forecast_id: id, forecast_history_status: forecast_history_status.REVOKED, created_by: req.user.id, updated_by: req.user.id } as any)
            // await ForecastBugdetDataHistory.update({ forecast_bugdet_data_history_status: forecast_budget_data_history_status.INACTIVE }, { where: { forecast_id: id } })

            message = 'SUCCESS_BUDGET_REVOKED'
        }


        /** check if user try to delete budget */
        if (forecast_budget_status == forecast_status.DELETED) {
            if (checkForecastExist.forecast_status == forecast_status.APPROVED) {
                return res.status(StatusCodes.FORBIDDEN).json({ status: false, message: res.__("ERROR_FORECAST_ALREADY_APPROVED", { type: 'deleted' }) });
            }

            /** check forecast status is request for approval mode */
            if (checkForecastExist.forecast_status == forecast_status.SUBMITTED) {
                return res.status(StatusCodes.FORBIDDEN).json({ status: false, message: res.__("ERROR_FORECAST_UNDER_APPROVAL", { type: 'deleted' }) });
            }
            /** Delete Budget */
            await Forecast.update({ forecast_status: forecast_status.DELETED }, { where: { id: id } })
            await ForecastAssignBudget.update({ user_assign_status: user_assign_status.INACTIVE }, { where: { forecast_id: id } })
            const forecastHistory = await ForecastHistory.create({ forecast_id: id, forecast_history_status: forecast_history_status.DELETED, created_by: req.user.id, updated_by: req.user.id } as any)
            /** Get Budget Forecast data to store history */
            const getBudgetForecastData = await ForecastBugdetData.findAll({ where: { forecast_id: id }, raw: true })
            for (const data of getBudgetForecastData) {
                await ForecastBugdetDataHistory.create({
                    payment_type_category_id: data.payment_type_category_id,
                    forecast_history_id: forecastHistory.id,
                    forecast_id: id,
                    payment_type_id: data.payment_type_id,
                    forecast_category_type: data.forecast_category_type,
                    forecast_bugdet_data_history_status: forecast_budget_data_history_status.INACTIVE,
                    forecast_category_status: data.forecast_category_status,
                    january_amount: data.january_amount, february_amount: data.february_amount, march_amount: data.march_amount, april_amount: data.april_amount, may_amount: data.may_amount, june_amount: data.june_amount, july_amount: data.july_amount, august_amount: data.august_amount, september_amount: data.september_amount, october_amount: data.october_amount, november_amount: data.november_amount, december_amount: data.december_amount, bugdet_target_amount: data.bugdet_target_amount,
                    created_by: req.user.id,
                    updated_by: req.user.id
                } as any)
            }
            await ForecastBugdetData.update({ forecast_bugdet_data_status: forecast_budget_data_status.INACTIVE }, { where: { forecast_id: id } })
            message = 'SUCCESS_BUDGET_DELETED'
        }

        return res.status(StatusCodes.OK).json({ status: false, message: res.__(message) });
    } catch (error) {
        console.log("error", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

/** Get delete budget data */
const deleteBudgetHistory = async (req: Request, res: Response) => {
    try {
        const { page, size, branch_id, forecast_year }: any = req.query;

        // Get pagination details
        const { limit, offset } = getPagination(page, size);

        // Define the query object for fetching forecast history
        const qryObj: any = {
            attributes: ['id', 'forecast_id', 'forecast_history_status', 'updatedAt'],
            include: [
                {
                    model: User,
                    as: "forecast_history_created_by",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                    ],
                    where: { organization_id: req.user.organization_id }
                },
            ],
            where: { forecast_history_status: forecast_history_status.DELETED },
            order: [['id', 'DESC']],
            raw: true,
            nest: true,
        };

        // Apply pagination if page and size are provided
        if (page && size) {
            qryObj.limit = Number(limit);
            qryObj.offset = Number(offset);
        }

        // Fetch forecast history from the database
        const { count, rows: getForecastHistoryResponse }: any = await ForecastHistory.findAndCountAll(qryObj);

        // Calculate total pages for pagination
        const { total_pages } = getPaginatedItems(
            Number(size),
            Number(page),
            count || 0,
        );

        // Parse the response to JSON
        const getForecastHistoryList = JSON.parse(JSON.stringify(getForecastHistoryResponse));

        const finalResponse = [];

        for (const forecast of getForecastHistoryList) {
            const getForecastData = await Forecast.findOne({
                where: { id: forecast.forecast_id },
                attributes: ['forecast_year', 'branch_id'],
                raw: true
            });

            if (getForecastData) {
                // Add the forecast data to the forecast history object
                forecast.forecastDetails = getForecastData;

                // Apply filters dynamically
                if (
                    (!branch_id || branch_id.split(',').map((id: any) => Number(id)).includes(getForecastData.branch_id)) &&
                    (!forecast_year || getForecastData.forecast_year == forecast_year)
                ) {
                    finalResponse.push(forecast);
                }
            }
        }

        // Respond with the filtered data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: finalResponse,
            count: finalResponse.length, // Updated count after filtering
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });

    } catch (error) {
        console.log("error", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }

}

export default {
    createForecast,
    getForecastById,
    getForecastList,
    getForecastCategoryByBranch,
    updateForecast,
    makeForecastLocked,
    getForecastBudgetDetails,
    downloadPdfExcel,
    saveForecastBudgetData,
    getForecastHistoryList,
    getForecastHistoryDetails,
    getForecastChartList,
    hasBudgetDataChanges,
    assignBudget,
    deleteBudget,
    deleteBudgetHistory
}