import { NextFunction, Request, Response } from "express";
import { getGeneralSetting } from "../helper/common";
const checkAppVersion = async (req: Request, res: Response, next: NextFunction) => {
    try {
        /**  App version check */
        const platformType = req.headers['platform-type'];
        const appVersion = req.headers['app-version'];
        const appIdentifier = req.headers['app-identifier'] || 'old'; // New header to identify which app (new or old)

        // Determine which app the user is using
        const findGeneralSetting: any = await getGeneralSetting()
        const isOldApp = appIdentifier === 'old';
        const isNewApp = appIdentifier === 'new';
        let appTransitionMode = findGeneralSetting?.APP_TRANSITION_MODE; // none/encourage/force

        let minAndroidVersion = findGeneralSetting?.MIN_ANDROID_VERSION;
        let maxAndroidVersion = findGeneralSetting?.MAX_ANDROID_VERSION;
        let minIosVersion = findGeneralSetting?.MIN_IOS_VERSION;
        let maxIosVersion = findGeneralSetting?.MAX_IOS_VERSION;
        let storeLinkIOS = findGeneralSetting?.APP_STORE_LINK_IOS;
        let storeLinkAndroid = findGeneralSetting?.APP_STORE_LINK_ANDROID;
        if (isOldApp && appTransitionMode != 'none') {
            storeLinkIOS = findGeneralSetting?.NEW_APP_STORE_LINK_IOS;
            storeLinkAndroid = findGeneralSetting?.NEW_APP_STORE_LINK_ANDROID;
        }
        let reviewAndroidVersion = findGeneralSetting?.REVIEW_ANDROID_VERSION;
        let reviewIosVersion = findGeneralSetting?.REVIEW_IOS_VERSION;

        if (isNewApp) {
            minAndroidVersion = findGeneralSetting?.NEW_MIN_ANDROID_VERSION;
            maxAndroidVersion = findGeneralSetting?.NEW_MAX_ANDROID_VERSION;
            minIosVersion = findGeneralSetting?.NEW_MIN_IOS_VERSION;
            maxIosVersion = findGeneralSetting?.NEW_MAX_IOS_VERSION;
            storeLinkIOS = findGeneralSetting?.NEW_APP_STORE_LINK_IOS;
            storeLinkAndroid = findGeneralSetting?.NEW_APP_STORE_LINK_ANDROID;
            appTransitionMode = findGeneralSetting?.NEW_APP_TRANSITION_MODE;
            reviewAndroidVersion = findGeneralSetting?.NEW_REVIEW_ANDROID_VERSION;
            reviewIosVersion = findGeneralSetting?.NEW_REVIEW_IOS_VERSION;
            appTransitionMode = 'none'
        }

        const isVersionOutOfBounds = (version: string, minVersion: string, maxVersion: string) => {
            return (version && minVersion && compareVersions(version, "<", minVersion)) ||
                (version && maxVersion && compareVersions(version, ">", maxVersion));
        };

        const isValidPlatform = (platform: any, version: any) => {
            switch (platform) {
                case "android":
                    return isVersionOutOfBounds(version, minAndroidVersion, maxAndroidVersion);
                case "ios":
                    return isVersionOutOfBounds(version, minIosVersion, maxIosVersion);
                default:
                    return false;
            }
        }
        if (req.headers['platform-type'] && req.headers['app-version']) {
            // Set review version in config only when app is in review  otherwise set it to empty
            if ((reviewAndroidVersion && reviewAndroidVersion == req.headers['app-version'] && platformType == "android") || (reviewIosVersion && reviewIosVersion == req.headers['app-version'] && platformType == "ios")) {
                res.setHeader("App-Version", "OK");
            } else {
                if (isValidPlatform(platformType, appVersion)) {
                    res.setHeader("App-Version", "Upgrade-Required");
                } else if (platformType === "android" && compareVersions(appVersion, "<", maxAndroidVersion)) {
                    res.setHeader("App-Version", "Upgrade");
                } else if (platformType === "ios" && compareVersions(appVersion, "<", maxIosVersion)) {
                    res.setHeader("App-Version", "Upgrade");
                } else {
                    res.setHeader("App-Version", "OK");
                }
            }
        } else {
            res.setHeader("App-Version", "OK");
        }
        // Set maintenance mode header based on maintenance-mode header and platform type
        if (req.headers['platform-type'] && req.headers['maintenance-mode']) {
            let maintenanceValue = "false";
            if (findGeneralSetting?.MAINTENANCE_MODE == "true") {
                maintenanceValue = "true";
            }
            res.setHeader("Maintenance-Mode", maintenanceValue);
        } else {
            res.setHeader("Maintenance-Mode", "false");
        }
        // App transition management
        res.setHeader("App-Transition-Mode", appTransitionMode);
        res.setHeader("App-Store-Link-iOS", storeLinkIOS);
        res.setHeader("App-Store-Link-Android", storeLinkAndroid);

        next()
    } catch (e) {
        return res.status(301).send({ status: false, message: e })
    }
}

function compareVersions(v1: any, comparator: string, v2: string) {
    "use strict";
    if (v1 && v2 && v1 != "" && v2 != "") {
        // const comparatory = comparator as any == '=' ? '==' : comparator;
        if (['==', '===', '<', '<=', '>', '>=', '!=', '!=='].indexOf(comparator) == -1) {
            throw new Error('Invalid comparator. ' + comparator);
        }
        const v1parts = v1.split('.'), v2parts = v2.split('.');
        const maxLen = Math.max(v1parts.length, v2parts.length);
        let part1, part2;
        let cmp = 0;
        for (let i = 0; i < maxLen && !cmp; i++) {
            part1 = parseInt(v1parts[i], 10) || 0;
            part2 = parseInt(v2parts[i], 10) || 0;
            if (part1 < part2)
                cmp = 1;
            if (part1 > part2)
                cmp = -1;
        }
        return eval('0' + comparator + cmp);
    } else {
        return false
    }
}

export default checkAppVersion