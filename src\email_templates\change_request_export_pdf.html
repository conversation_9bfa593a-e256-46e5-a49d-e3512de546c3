<!DOCTYPE html>
<html>

<head>
    <title>Change Request Report</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .report-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .table-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            max-width: 100%;
            margin: 0 auto;
            overflow-x: auto;
        }

        .dsr-table {
            width: 100%;
            padding-top: 3px;
            padding-bottom: 3px;
        }

        th,
        td {
            border: 1px solid #dddddd;
            text-align: center;
            word-wrap: break-word;
            padding: 8px;
            font-size: 12px;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .total-row {
            font-weight: bold;
            background-color: #e6ffe6;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
        }

        .logo-container img {
            max-width: 100px;
        }

        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: center;
            flex-grow: 1;
            margin-top: 20px;
        }

        .report-details {
            text-align: right;
            font-size: 12px;
            color: #666;
        }

        .report-details p {
            margin: 2px 0;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #999;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-awaiting {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>

<body>
    <div class="report-container">
        <div class="header">
            <div class="report-title">Change Request Report</div>
            <div class="logo-container">
                <img src="{{NAMASTE_LOGO}}">
            </div>
            <div>
                <div class="report-details" style="margin-bottom: 10px;">
                    <p>Date: {{current_date}}</p>
                </div>
                <div class="report-details">
                    {{#if filters_applied}}
                    {{#each filters_applied}}
                    <p>{{this}}</p>
                    {{/each}}
                    {{else}}
                    <p>Filters Applied: None</p>
                    {{/if}}
                </div>
            </div>
        </div>
        <div class="table-container">
            <table class="dsr-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Subject</th>
                        <th>Date</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each change_requests}}
                    <tr>
                        <td>{{id}}</td>
                        <td>{{user_full_name}}</td>
                        <td>{{change_request_subject}}</td>
                        <td>{{createdAt}}</td>
                        <td>
                            <span class="status-badge {{status_class}}">
                                {{change_request_status}}
                            </span>
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        <div class="footer">
            <p>{{confidentiality_statement}}</p>
            <p>Generated by {{generated_by}} • {{generated_at}}</p>
        </div>
    </div>
</body>

</html>
