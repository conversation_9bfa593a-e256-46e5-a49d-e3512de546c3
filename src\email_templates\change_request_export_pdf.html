<!DOCTYPE html>
<html>

<head>
    <title>Change Request Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }

        .table-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            max-width: 100%;
            margin: 0 auto;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .dsr-table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 16px 12px;
            text-align: center;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            position: relative;
        }

        th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        td {
            padding: 14px 12px;
            text-align: center;
            font-size: 13px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            color: #334155;
            font-weight: 500;
        }

        tr:nth-child(even) {
            background-color: #f8fafc;
        }

        tr:hover {
            background-color: #e2e8f0;
            transition: background-color 0.2s ease;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 25px;
            position: relative;
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .logo-container img {
            max-width: 100px;
            border-radius: 8px;
        }

        .report-title {
            text-align: center;
            font-size: 26px;
            color: #1e293b;
            font-weight: 700;
            position: absolute;
            left: 0;
            right: 0;
            top: 25px;
        }

        .report-details {
            text-align: right;
            font-size: 13px;
            color: #64748b;
            font-weight: 500;
        }

        .report-details p {
            margin: 3px 0;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-pending {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .status-awaiting {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
    </style>
</head>

<body>
    <div class="report-container">
        <div class="header">
            <div class="report-title">Change Request Report</div>
            <div class="logo-container">
                <img src="{{NAMASTE_LOGO}}" alt="Organization Logo">
            </div>
            <div>
                <div class="report-details" style="margin-bottom: 10px;">
                    <p><strong>Date:</strong> {{current_date}}</p>
                </div>
                <div class="report-details">
                    {{#if filters_applied}}
                    {{#each filters_applied}}
                    <p>{{this}}</p>
                    {{/each}}
                    {{else}}
                    <p>Filters Applied: None</p>
                    {{/if}}
                </div>
            </div>
        </div>
        <div class="table-container">
                <table class="dsr-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Subject</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each change_requests}}
                        <tr>
                            <td><strong>{{id}}</strong></td>
                            <td>{{user_full_name}}</td>
                            <td>{{change_request_subject}}</td>
                            <td>{{createdAt}}</td>
                            <td>
                                <span class="status-badge {{status_class}}">
                                    {{change_request_status}}
                                </span>
                            </td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
