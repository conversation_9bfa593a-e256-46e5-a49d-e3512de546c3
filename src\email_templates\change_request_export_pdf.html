<!DOCTYPE html>
<html>

<head>
    <title>Change Request Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #ffffff;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .logo-container {
            position: relative;
            z-index: 2;
        }

        .logo-container img {
            max-width: 80px;
            max-height: 80px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .report-title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            flex-grow: 1;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .report-details {
            text-align: right;
            font-size: 13px;
            position: relative;
            z-index: 2;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .report-details p {
            margin: 3px 0;
            font-weight: 500;
        }

        .content-section {
            padding: 40px;
        }

        .table-container {
            background: #ffffff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e1e5e9;
        }

        .dsr-table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 18px 15px;
            text-align: center;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            position: relative;
        }

        th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        td {
            padding: 15px;
            text-align: center;
            font-size: 13px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
            transition: background-color 0.2s ease;
        }

        tr:hover td {
            background-color: #f8fafc;
        }

        tr:nth-child(even) {
            background-color: #fafbfc;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-pending {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .status-awaiting {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .footer {
            background: #f8fafc;
            padding: 25px 40px;
            text-align: center;
            border-top: 1px solid #e1e5e9;
            color: #6b7280;
            font-size: 12px;
        }

        .footer p {
            margin: 5px 0;
        }

        .footer .confidentiality {
            font-weight: 600;
            color: #374151;
        }
    </style>
</head>

<body>
    <div class="report-container">
        <div class="header">
            <div class="logo-container">
                <img src="{{NAMASTE_LOGO}}" alt="Organization Logo">
            </div>
            <div class="report-title">Change Request Report</div>
            <div class="report-details">
                <p><strong>Date:</strong> {{current_date}}</p>
                {{#if filters_applied}}
                <p><strong>Filters:</strong></p>
                {{#each filters_applied}}
                <p>• {{this}}</p>
                {{/each}}
                {{else}}
                <p><strong>Filters:</strong> None Applied</p>
                {{/if}}
            </div>
        </div>

        <div class="content-section">
            <div class="table-container">
                <table class="dsr-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Employee Name</th>
                            <th>Request Subject</th>
                            <th>Submission Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each change_requests}}
                        <tr>
                            <td><strong>{{id}}</strong></td>
                            <td>{{user_full_name}}</td>
                            <td>{{change_request_subject}}</td>
                            <td>{{createdAt}}</td>
                            <td>
                                <span class="status-badge {{status_class}}">
                                    {{change_request_status}}
                                </span>
                            </td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p class="confidentiality">{{CONFIDENTIALITY_STATEMENT}}</p>
            <p>Generated by {{GENERATED_BY_USER}} • {{current_date}}</p>
        </div>
    </div>
</body>
</html>
