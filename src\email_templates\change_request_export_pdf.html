<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Request Report - Microffice</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 13px;
            line-height: 1.5;
            color: #374151;
            background: #f8fafc;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: white;
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
        }

        .brand-text {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }

        .report-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }

        .report-info {
            display: flex;
            justify-content: space-between;
            padding: 20px 32px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-item {
            text-align: center;
        }

        .info-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .filters-section {
            padding: 16px 32px;
            background: #eff6ff;
            border-bottom: 1px solid #e5e7eb;
        }

        .filters-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .filter-tag {
            background: #3b82f6;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .table-container {
            padding: 0 32px 32px 32px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 24px;
        }

        th {
            background: #f9fafb;
            color: #374151;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e5e7eb;
        }

        td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 13px;
            vertical-align: middle;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 500;
            text-transform: capitalize;
            display: inline-block;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-awaiting {
            background: #fecaca;
            color: #991b1b;
        }

        .footer {
            padding: 24px 32px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            margin-top: auto;
        }

        .footer-text {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .footer-brand {
            font-size: 11px;
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-section">
                <div class="logo">
                    <img src="{{NAMASTE_LOGO}}" alt="Logo">
                </div>
                <div class="brand-text">Microffice</div>
            </div>
            <div class="report-title">Change Request Report</div>
        </div>

        <div class="report-info">
            <div class="info-item">
                <div class="info-label">Generated At</div>
                <div class="info-value">{{current_date}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Generated By</div>
                <div class="info-value">{{GENERATED_BY_USER}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Total Records</div>
                <div class="info-value">{{change_requests.length}}</div>
            </div>
        </div>

        {{#if filters_applied}}
        <div class="filters-section">
            <div class="filters-title">Applied Filters</div>
            <div class="filter-tags">
                {{#each filters_applied}}
                <span class="filter-tag">{{this}}</span>
                {{/each}}
            </div>
        </div>
        {{/if}}

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Subject</th>
                        <th>Date</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each change_requests}}
                    <tr>
                        <td>{{id}}</td>
                        <td>{{user_full_name}}</td>
                        <td>{{change_request_subject}}</td>
                        <td>{{createdAt}}</td>
                        <td>
                            <span class="status-badge {{status_class}}">
                                {{change_request_status}}
                            </span>
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>

    </div>
