<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Report - Microffice</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 13px;
            line-height: 1.5;
            color: #374151;
            background: #f8fafc;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: white;
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .brand-text {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }

        .report-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }

        .report-info {
            display: flex;
            justify-content: space-between;
            padding: 20px 32px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-item {
            text-align: center;
        }

        .info-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .filters-section {
            padding: 16px 32px;
            background: #eff6ff;
            border-bottom: 1px solid #e5e7eb;
        }

        .filters-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .filter-tag {
            background: #3b82f6;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .table-container {
            padding: 0 32px 32px 32px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        th {
            background: #f9fafb;
            color: #374151;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e5e7eb;
        }

        td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 13px;
            vertical-align: middle;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 12px;
        }
        
        .status-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-active {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-inactive {
            background: #ffebee;
            color: #c62828;
        }
        
        .status-pending {
            background: #fff3e0;
            color: #ef6c00;
        }
        
        .footer {
            margin-top: 30px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
            text-align: center;
            font-size: 11px;
            color: #666;
        }
        
        .summary {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .summary-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        @media print {
            .header {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            th {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{title}}</h1>
        <p>Comprehensive User Report</p>
    </div>
    
    <div class="report-info">
        <div>
            <strong>Generated At:</strong>
            {{generated_at}}
        </div>
        <div>
            <strong>Generated By:</strong>
            {{generated_by}}
        </div>
        <div>
            <strong>Total Records:</strong>
            {{total_records}}
        </div>
    </div>
    
    {{#if filters}}
    <div class="filters">
        <h3>Applied Filters</h3>
        <div class="filter-tags">
            {{#each filters}}
            <span class="filter-tag">{{this}}</span>
            {{/each}}
        </div>
    </div>
    {{/if}}
    
    <div class="summary">
        <div class="summary-item">
            <span class="summary-number">{{total_records}}</span>
            <div class="summary-label">Total Users</div>
        </div>
        <div class="summary-item">
            <span class="summary-number">{{active_users}}</span>
            <div class="summary-label">Active Users</div>
        </div>
        <div class="summary-item">
            <span class="summary-number">{{inactive_users}}</span>
            <div class="summary-label">Inactive Users</div>
        </div>
    </div>
    
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>S.No</th>
                    <th>Emp. No</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Branch</th>
                    <th>Department</th>
                    <th>Role</th>
                    <th>Joining Date</th>
                    <th>Status</th>
                    {{#if show_invitation}}
                    <th>Invitation</th>
                    {{/if}}
                </tr>
            </thead>
            <tbody>
                {{#each users}}
                <tr>
                    <td>{{@index_plus_one}}</td>
                    <td>{{employment_number}}</td>
                    <td>{{user_full_name}}</td>
                    <td>{{user_email}}</td>
                    <td>{{user_phone_number}}</td>
                    <td>{{branch_name}}</td>
                    <td>{{department_name}}</td>
                    <td>{{role_name}}</td>
                    <td>{{user_joining_date}}</td>
                    <td>
                        <span class="status-badge {{status_class}}">
                            {{user_status}}
                        </span>
                    </td>
                    {{#if ../show_invitation}}
                    <td>
                        {{#if invitation_status}}
                        <span class="status-badge status-{{invitation_status}}">
                            {{invitation_status}}
                        </span>
                        {{else}}
                        <span class="status-badge status-pending">N/A</span>
                        {{/if}}
                    </td>
                    {{/if}}
                </tr>
                {{/each}}
            </tbody>
        </table>
    </div>
    
    <div class="footer">
        <p><strong>{{confidentiality_statement}}</strong></p>
        <p>This report was generated automatically by the system on {{generated_at}}</p>
    </div>
</body>
</html>
