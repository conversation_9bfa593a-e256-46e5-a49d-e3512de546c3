import { Router } from "express";
import changeRequestController from "../../controller/changeRequest.controller";
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";
const router: Router = Router();

const uploadService = multerS3(
  process.env.NODE_ENV!,
  FILE_UPLOAD_CONSTANT.CHANGE_REQUEST_FILES.folder,
);

// add change request
router.post(
  "/send-change-request/:change_request_id?",
  uploadService.array("change_request_files", 5),
  changeRequestController.sendChangeRequest,
);

// get change request list
router.get(
  "/get-change-request-list",
  changeRequestController.getAllChangeRequest,
);

// get change request list
router.get(
  "/get-own-change-request-list",
  changeRequestController.getOwnChangeRequestList,
);

router.put(
  "/update-change-request/:change_request_id",
  uploadService.array("change_request_files", 5),
  changeRequestController.updateRejectedRequest,
);

// get change request list
router.get(
  "/get-change-request-by-id/:change_request_id",
  changeRequestController.getChangeRequestById,
);

// approve reject change request
router.post(
  "/approve-reject-change-request/:change_request_id",
  changeRequestController.approveRejectChangeRequest,
);

//  change request history
router.get(
  "/get-change-request-history/:change_request_id",
  changeRequestController.getChangeRequestHistoryById,
);

//  delete request history
router.delete(
  "/delete-change-request-by-id/:change_request_id",
  changeRequestController.deleteChangeRequestById,
);

router.get("/get-change-request-fields", changeRequestController.getChangeRequestFields);

router.post("/store-change-request-fields", changeRequestController.storeChangeRequestSettings);

router.get("/get-stored-change-request-field", changeRequestController.getStoredChangeRequestField);

// export change requests
router.get("/export-change-requests", changeRequestController.exportChangeRequests);

export default router;
